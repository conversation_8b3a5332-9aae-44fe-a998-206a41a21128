1.基础配置文件：

### env
/Users/<USER>/git/afireai_api/.env

### main
/Users/<USER>/git/afireai_api/main.py

### 依赖管理文件
/Users/<USER>/git/afireai_api/requirements.txt

### database 设定
/Users/<USER>/git/afireai_api/database/db.py

### routers 路由文件
/Users/<USER>/git/afireai_api/routers/models_m.py
/Users/<USER>/git/afireai_api/routers/user.py
/Users/<USER>/git/afireai_api/routers/upload_file.py
/Users/<USER>/git/afireai_api/routers/messages.py
/Users/<USER>/git/afireai_api/routers/api_keys.py
/Users/<USER>/git/afireai_api/routers/meeting_summary.py
/Users/<USER>/git/afireai_api/routers/chat_sessions.py
/Users/<USER>/git/afireai_api/routers/meeting_files.py

2.AI核心服务：

### ai关联service
/Users/<USER>/git/afireai_api/ai/chromadb_service.py
/Users/<USER>/git/afireai_api/ai/audio_service.py
/Users/<USER>/git/afireai_api/ai/langchain_interaction.py
/Users/<USER>/git/afireai_api/ai/chatgpt_service.py
/Users/<USER>/git/afireai_api/ai/watcher.py

### models 文件
/Users/<USER>/git/afireai_api/models/models_m.py
/Users/<USER>/git/afireai_api/models/user.py
/Users/<USER>/git/afireai_api/models/token.py

/Users/<USER>/git/afireai_api/models/upload_file.py
/Users/<USER>/git/afireai_api/models/messages.py
/Users/<USER>/git/afireai_api/models/user_info.py
/Users/<USER>/git/afireai_api/models/api_keys.py
/Users/<USER>/git/afireai_api/models/meeting_summary.py
/Users/<USER>/git/afireai_api/models/chat_sessions.py
/Users/<USER>/git/afireai_api/models/processing_status.py
/Users/<USER>/git/afireai_api/models/meeting_files.py

### schemas 文件
/Users/<USER>/git/afireai_api/schemas/models_m.py
/Users/<USER>/git/afireai_api/schemas/user.py
/Users/<USER>/git/afireai_api/schemas/upload_file.py
/Users/<USER>/git/afireai_api/schemas/messages.py
/Users/<USER>/git/afireai_api/schemas/user_info.py
/Users/<USER>/git/afireai_api/schemas/meeting_summary.py
/Users/<USER>/git/afireai_api/schemas/chat_sessions.py
/Users/<USER>/git/afireai_api/schemas/processing_status.py
/Users/<USER>/git/afireai_api/schemas/meeting_files.py

### utils 文件
/Users/<USER>/git/afireai_api/utils/auth.py
/Users/<USER>/git/afireai_api/utils/utils.py


1.	必要なパッケージをインストールします。

pip install -r requirements.txt

2.	サーバーを起動します。
uvicorn main:app --reload
python3.13 -m uvicorn main:app --reload


python3.13 -m uvicorn main:app --reload --reload-exclude *.git*


トークンの説明: ifire-aiに利用するAPI token
トークンID: o9ein7ymytb8xbd3duix7xc3fw
アクセストークン: mxi4wdni8fnftn8n8eq7yn7xco
