# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# python cache
/__pycache__
/*/__pycache__
/*/*/__pycache__
/*/*/*/__pycache__
/*/*/*/*/__pycache__
/*/*/*/*/*/__pycache__

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
dev

.vscode
.idea

# docker-compose env files
# .env

*.key
*.key.pub

masks.json

test/*
venv/*
