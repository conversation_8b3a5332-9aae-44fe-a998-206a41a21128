#!/usr/bin/env python3
# test_orchestrator_workflow_run_id.py - 测试 orchestrator 是否正确传递 workflowRunId

import asyncio
import json
from sqlalchemy.orm import Session
from database.db import get_db
from utils.orchestrator import Orchestrator
from schemas.workflow_runs import WorkflowRun<PERSON>reate
from service.workflow_runs_service import create_workflow_run

async def test_orchestrator_workflow_run_id():
    """测试 orchestrator 是否正确传递 workflowRunId"""

    # 获取数据库会话
    db = next(get_db())

    try:
        # 创建测试工作流运行记录
        workflow_run_data = WorkflowRunCreate(
            user_id="1024",
            app_id="test-app-123",
            workflow_run_id="test-workflow-run-789",
            status="PENDING",
            params="{}",
            memory="",
            ip_addr="127.0.0.1",
            flow_json=json.dumps({
                "nodes": [
                    {
                        "id": "start-node-1",
                        "type": "startNode",
                        "data": {"label": "开始节点"}
                    },
                    {
                        "id": "end-node-1",
                        "type": "endNode",
                        "data": {"label": "结束节点"}
                    }
                ],
                "edges": [
                    {
                        "id": "edge-1",
                        "source": "start-node-1",
                        "target": "end-node-1"
                    }
                ]
            })
        )

        # 创建工作流运行记录
        workflow_run = create_workflow_run(db, workflow_run_data)
        print(f"创建工作流运行记录: {workflow_run.workflow_run_id}")

        # 创建 orchestrator 实例
        orchestrator = Orchestrator(db)

        # 运行工作流
        print(f"开始执行工作流: {workflow_run.workflow_run_id}")
        await orchestrator.run_workflow(
            app_id=workflow_run.app_id,
            workflow_run_id=workflow_run.workflow_run_id,
            flow_json_str=workflow_run.flow_json
        )

        print("工作流执行完成！")

    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_orchestrator_workflow_run_id())
