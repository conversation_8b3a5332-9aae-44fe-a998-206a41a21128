import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# .envファイルを読み込む
load_dotenv()

DATABASE_URL = os.getenv("__DATABASE_URL")

# 建议:
# 1) pool_pre_ping=True：在每次取连接前先执行一次 "SELECT 1" 测试，若连接失效则自动丢弃并重建连接
# 2) pool_recycle=3600：连接在存活3600秒(1小时)后重置, 避免 MySQL wait_timeout
# 3) 在 connect_args 里可设置连接/读/写超时
engine = create_engine(
    DATABASE_URL,
    isolation_level='READ COMMITTED',
    pool_pre_ping=True,
    pool_recycle=3600,  # 1小时回收一次连接，可按需调整
    connect_args={
        "connect_timeout": 10,  # 建立连接的超时时间(秒)
        "read_timeout": 120,    # 读操作超时(秒)
        "write_timeout": 120,   # 写操作超时(秒)
    }
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()