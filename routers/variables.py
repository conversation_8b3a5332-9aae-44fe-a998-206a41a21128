# routers/variables.py

from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict
import uuid

from database.db import get_db
from models.variables import Variable
from schemas.variables import VariableCreate, VariableUpdate, VariableResponse
from service.variables_service import (
    create_variable, 
    update_variable,
    update_variable_by_key,
    get_variable, 
    get_variable_by_key,
    get_variables_by_user,
    get_variables_by_workflow_run,
    get_variables_dict_by_workflow_run,
    delete_variable
)
from utils.auth import get_current_user

router = APIRouter()

@router.post("/create", response_model=VariableResponse)
async def create_new_variable(
    variable_data: VariableCreate,
    req: Request,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """新しい変数を作成する"""
    # ユーザーIDを現在認証されているユーザーに設定
    variable_data.user_id = current_user_id
    # クライアントのIPアドレスを設定
    variable_data.ip_addr = req.client.host
    
    return create_variable(db, variable_data)

@router.get("/list", response_model=List[VariableResponse])
async def list_variables(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """現在のユーザーの変数一覧を取得する"""
    return get_variables_by_user(db, current_user_id, skip, limit)

@router.get("/workflow-run/{workflow_run_id}", response_model=List[VariableResponse])
async def list_variables_by_workflow_run(
    workflow_run_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたワークフロー実行IDに関連する変数一覧を取得する"""
    variables = get_variables_by_workflow_run(db, workflow_run_id, skip, limit)
    
    # 権限チェック: 自分の変数のみアクセス可能
    # 最初の変数のユーザーIDをチェック（変数が存在する場合）
    if variables and variables[0].user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to access these variables")
    
    return variables

@router.get("/workflow-run/{workflow_run_id}/dict")
async def get_variables_dict(
    workflow_run_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたワークフロー実行IDに関連する変数を辞書形式で取得する"""
    variables = get_variables_by_workflow_run(db, workflow_run_id)
    
    # 権限チェック: 自分の変数のみアクセス可能
    # 最初の変数のユーザーIDをチェック（変数が存在する場合）
    if variables and variables[0].user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to access these variables")
    
    return get_variables_dict_by_workflow_run(db, workflow_run_id)

@router.get("/workflow-run/{workflow_run_id}/key/{var_key}", response_model=VariableResponse)
async def get_variable_by_key_endpoint(
    workflow_run_id: str,
    var_key: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたワークフロー実行IDとキーで変数を取得する"""
    db_variable = get_variable_by_key(db, workflow_run_id, var_key)
    
    if not db_variable:
        raise HTTPException(status_code=404, detail="Variable not found")
    
    # 権限チェック: 自分の変数のみアクセス可能
    if db_variable.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to access this variable")
    
    return db_variable

@router.get("/{variable_id}", response_model=VariableResponse)
async def get_variable_by_id(
    variable_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたIDの変数を取得する"""
    db_variable = get_variable(db, variable_id)
    
    if not db_variable:
        raise HTTPException(status_code=404, detail="Variable not found")
    
    # 権限チェック: 自分の変数のみアクセス可能
    if db_variable.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to access this variable")
    
    return db_variable

@router.put("/{variable_id}", response_model=VariableResponse)
async def update_variable_by_id(
    variable_id: str,
    variable_data: VariableUpdate,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたIDの変数を更新する"""
    # まず、変数が存在するか確認
    db_variable = get_variable(db, variable_id)
    
    if not db_variable:
        raise HTTPException(status_code=404, detail="Variable not found")
    
    # 権限チェック: 自分の変数のみ更新可能
    if db_variable.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to update this variable")
    
    # 変数を更新
    updated_variable = update_variable(db, variable_id, variable_data)
    
    return updated_variable

@router.put("/workflow-run/{workflow_run_id}/key/{var_key}", response_model=VariableResponse)
async def update_variable_by_key_endpoint(
    workflow_run_id: str,
    var_key: str,
    variable_data: VariableUpdate,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたワークフロー実行IDとキーで変数を更新する"""
    # まず、変数が存在するか確認
    db_variable = get_variable_by_key(db, workflow_run_id, var_key)
    
    if not db_variable:
        raise HTTPException(status_code=404, detail="Variable not found")
    
    # 権限チェック: 自分の変数のみ更新可能
    if db_variable.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to update this variable")
    
    # 変数を更新
    updated_variable = update_variable_by_key(db, workflow_run_id, var_key, variable_data)
    
    return updated_variable

@router.delete("/{variable_id}")
async def delete_variable_by_id(
    variable_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたIDの変数を削除する"""
    # まず、変数が存在するか確認
    db_variable = get_variable(db, variable_id)
    
    if not db_variable:
        raise HTTPException(status_code=404, detail="Variable not found")
    
    # 権限チェック: 自分の変数のみ削除可能
    if db_variable.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this variable")
    
    # 変数を削除
    success = delete_variable(db, variable_id)
    
    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete variable")
    
    return {"message": "Variable deleted successfully"}
