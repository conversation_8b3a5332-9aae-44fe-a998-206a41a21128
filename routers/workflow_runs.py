# routers/workflow_runs.py

from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import uuid

from database.db import get_db
from models.workflow_runs import WorkflowRun
from schemas.workflow_runs import WorkflowRun<PERSON><PERSON>, WorkflowRunUpdate, WorkflowRunResponse
from service.workflow_runs_service import (
    create_workflow_run, 
    update_workflow_run, 
    get_workflow_run, 
    get_workflow_runs_by_user,
    get_workflow_runs_by_app,
    delete_workflow_run
)
from utils.auth import get_current_user

router = APIRouter()

@router.post("/create", response_model=WorkflowRunResponse)
async def create_new_workflow_run(
    workflow_run_data: WorkflowRunCreate,
    req: Request,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """新しいワークフロー実行を作成する"""
    # ユーザーIDを現在認証されているユーザーに設定
    workflow_run_data.user_id = current_user_id
    # クライアントのIPアドレスを設定
    workflow_run_data.ip_addr = req.client.host
    
    return create_workflow_run(db, workflow_run_data)

@router.get("/list", response_model=List[WorkflowRunResponse])
async def list_workflow_runs(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """現在のユーザーのワークフロー実行一覧を取得する"""
    return get_workflow_runs_by_user(db, current_user_id, skip, limit)

@router.get("/app/{app_id}", response_model=List[WorkflowRunResponse])
async def list_workflow_runs_by_app(
    app_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたアプリIDに関連するワークフロー実行一覧を取得する"""
    # アプリの所有者チェックは省略（必要に応じて追加）
    return get_workflow_runs_by_app(db, app_id, skip, limit)

@router.get("/{workflow_run_id}", response_model=WorkflowRunResponse)
async def get_workflow_run_by_id(
    workflow_run_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたIDのワークフロー実行を取得する"""
    db_workflow_run = get_workflow_run(db, workflow_run_id)
    
    if not db_workflow_run:
        raise HTTPException(status_code=404, detail="Workflow run not found")
    
    # 権限チェック: 自分のワークフロー実行のみアクセス可能
    if db_workflow_run.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to access this workflow run")
    
    return db_workflow_run

@router.put("/{workflow_run_id}", response_model=WorkflowRunResponse)
async def update_workflow_run_by_id(
    workflow_run_id: str,
    workflow_run_data: WorkflowRunUpdate,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたIDのワークフロー実行を更新する"""
    # まず、ワークフロー実行が存在するか確認
    db_workflow_run = get_workflow_run(db, workflow_run_id)
    
    if not db_workflow_run:
        raise HTTPException(status_code=404, detail="Workflow run not found")
    
    # 権限チェック: 自分のワークフロー実行のみ更新可能
    if db_workflow_run.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to update this workflow run")
    
    # ワークフロー実行を更新
    updated_workflow_run = update_workflow_run(db, workflow_run_id, workflow_run_data)
    
    return updated_workflow_run

@router.delete("/{workflow_run_id}")
async def delete_workflow_run_by_id(
    workflow_run_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたIDのワークフロー実行を削除する"""
    # まず、ワークフロー実行が存在するか確認
    db_workflow_run = get_workflow_run(db, workflow_run_id)
    
    if not db_workflow_run:
        raise HTTPException(status_code=404, detail="Workflow run not found")
    
    # 権限チェック: 自分のワークフロー実行のみ削除可能
    if db_workflow_run.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this workflow run")
    
    # ワークフロー実行を削除
    success = delete_workflow_run(db, workflow_run_id)
    
    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete workflow run")
    
    return {"message": "Workflow run deleted successfully"}
