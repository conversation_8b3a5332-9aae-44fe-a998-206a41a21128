# routers/apps.py

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import List
import uuid

from database.db import get_db
from models.apps import App
from schemas.apps import AppCreate, AppUpdate, AppResponse
from service.apps_service import (
    create_app, 
    update_app, 
    get_app, 
    get_apps_by_user, 
    delete_app
)
from utils.auth import get_current_user

router = APIRouter()

@router.post("/create", response_model=AppResponse)
async def create_new_app(
    app_data: AppCreate,
    req: Request,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """新しいアプリを作成する"""
    # ユーザーIDを現在認証されているユーザーに設定
    app_data.user_id = current_user_id
    # クライアントのIPアドレスを設定
    app_data.ip_addr = req.client.host
    
    return create_app(db, app_data)

@router.get("/list", response_model=List[AppResponse])
async def list_apps(
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """現在のユーザーのアプリ一覧を取得する"""
    return get_apps_by_user(db, current_user_id)

@router.get("/{app_id}", response_model=AppResponse)
async def get_app_by_id(
    app_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたIDのアプリを取得する"""
    db_app = get_app(db, current_user_id,app_id)
    
    if not db_app:
        raise HTTPException(status_code=404, detail="App not found")
    
    # 権限チェック: 自分のアプリのみアクセス可能
    if db_app.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to access this app")
    
    return db_app

@router.put("/{app_id}", response_model=AppResponse)
async def update_app_by_id(
    app_id: str,
    app_data: AppUpdate,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたIDのアプリを更新する"""
    # まず、アプリが存在するか確認
    db_app = get_app(db, current_user_id, app_id)
    
    if not db_app:
        raise HTTPException(status_code=404, detail="App not found")
    
    # 権限チェック: 自分のアプリのみ更新可能
    if db_app.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to update this app")
    
    # アプリを更新
    updated_app = update_app(db, app_id, app_data)
    
    return updated_app

@router.delete("/{app_id}")
async def delete_app_by_id(
    app_id: str,    
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """指定されたIDのアプリを削除する"""
    # まず、アプリが存在するか確認
    db_app = get_app(db, app_id)
    
    if not db_app:
        raise HTTPException(status_code=404, detail="App not found")
    
    # 権限チェック: 自分のアプリのみ削除可能
    if db_app.user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this app")
    
    # アプリを削除
    success = delete_app(db, app_id)
    
    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete app")
    
    return {"message": "App deleted successfully"}
