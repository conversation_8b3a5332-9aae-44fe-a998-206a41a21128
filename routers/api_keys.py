# routers/api_keys.py

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from utils.auth import generate_api_key,get_current_user

from database.db import get_db
from models.api_keys import APIKey
from schemas.api_keys import APIKeyCreate, APIKeyResponse
from datetime import datetime

router = APIRouter()

@router.post("/create", response_model=APIKeyResponse)
async def create_api_key(
    request: APIKeyCreate, 
    req: Request,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)  # 認証されたユーザーID
):
    """新しいAPIキーを作成し、リクエスト元のIPを記録"""
    new_api_key = generate_api_key()
    client_ip = req.client.host

    api_key_entry = APIKey(
        user_id=current_user_id,
        api_name=request.api_name,
        api_key=new_api_key,
        expiration=None,
        memo="",
        ip_addr=client_ip,
    )
    db.add(api_key_entry)
    db.commit()
    db.refresh(api_key_entry)

    return api_key_entry

@router.get("/list", response_model=list[APIKeyResponse])
async def get_api_keys(
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user)  # login認証
):
    """ユーザーのAPIキー一覧を取得"""
    return db.query(APIKey).filter(APIKey.user_id == user_id).all()

@router.delete("/delete/{api_key}")
async def delete_api_key(
    api_key: str, 
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user)  # APIキーで認証
):
    """APIキーを削除"""
    api_key_entry = db.query(APIKey).filter(APIKey.api_key == api_key, APIKey.user_id == user_id).first()
    if not api_key_entry:
        raise HTTPException(status_code=404, detail="API key not found")

    db.delete(api_key_entry)
    db.commit()

    return {"message": "API key deleted successfully"}