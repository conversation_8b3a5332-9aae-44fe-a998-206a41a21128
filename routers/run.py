# routers/run.py

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
import asyncio
import uuid
from datetime import datetime, timezone
import json
import logging

from database.db import get_db
from models.workflow_runs import WorkflowRun
from models.workflow_run_nodes import WorkflowRunNode
from schemas.workflow_runs import WorkflowRunCreate
from service.apps_service import get_app
from service.workflow_runs_service import create_workflow_run
from utils.orchestrator import Orchestrator
from utils.auth import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/{workflow_id}/workflow", status_code=200)
async def run_workflow(
    workflow_id: str,
    req: Request,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """
    ワークフローを実行する

    Args:
        workflow_id (str): ワークフローID（app_id）
        req (Request): リクエスト
        db (Session): データベースセッション
        current_user_id (str): 現在のユーザーID

    Returns:
        Dict: ワークフロー実行情報
    """
    # アプリの取得
    app = get_app(db, current_user_id, workflow_id)

    if not app:
        raise HTTPException(status_code=404, detail="Workflow not found")

    # flow_jsonの取得
    flow_json = app.flow_json

    # ワークフロー実行レコードの作成
    workflow_run_data = WorkflowRunCreate(
        user_id=current_user_id,
        app_id=workflow_id,
        status="RUNNING",
        params="",
        memory="",
        flow_json=flow_json,
        finished_at=datetime.now(timezone.utc),  # 仮の値、後で更新
        ip_addr=req.client.host
    )
    workflow_run = create_workflow_run(db, workflow_run_data)

    # オーケストレーターの初期化と実行
    orchestrator = Orchestrator(db)

    # 非同期タスクとしてワークフロー実行を開始
    asyncio.create_task(orchestrator.run_workflow(workflow_id, workflow_run.workflow_run_id, flow_json))

    # 即時レスポンスを返す
    return {
        "workflow_id": workflow_id,
        "workflow_run_id": workflow_run.workflow_run_id,
        "status": "SUCCESS"
    }

@router.get("/{workflow_id}/status/{workflow_run_id}")
async def get_workflow_run_status(
    workflow_id: str,
    workflow_run_id: str,
    db: Session = Depends(get_db),
    current_user_id: str = Depends(get_current_user)
):
    """
    ワークフロー実行のステータスを取得する

    Args:
        workflow_id (str): ワークフローID（app_id）
        workflow_run_id (str): ワークフロー実行ID
        db (Session): データベースセッション
        current_user_id (str): 現在のユーザーID

    Returns:
        Dict: ワークフロー実行ステータス
    """
    # アプリの取得
    app = get_app(db, current_user_id, workflow_id)

    if not app:
        raise HTTPException(status_code=404, detail="Workflow not found")

    # ワークフロー実行の取得（修正：正しいフィルター条件）
    workflow_run = db.query(WorkflowRun).filter(
        WorkflowRun.workflow_run_id == workflow_run_id,
        WorkflowRun.app_id == workflow_id
    ).first()

    if not workflow_run:
        raise HTTPException(status_code=404, detail="Workflow run not found")

    # ワークフロー実行ノードの取得
    workflow_run_nodes = db.query(WorkflowRunNode).filter(
        WorkflowRunNode.workflow_run_id == workflow_run_id
    ).all()

    # ノードの状態をカウント
    node_stats = {
        "total": len(workflow_run_nodes),
        "completed": sum(1 for node in workflow_run_nodes if node.status == "SUCCESS"),
        "running": sum(1 for node in workflow_run_nodes if node.status == "RUNNING"),
        "failed": sum(1 for node in workflow_run_nodes if node.status == "FAILED"),
        "pending": sum(1 for node in workflow_run_nodes if node.status == "PENDING" or node.status is None)
    }

    # 各ノードの詳細状態を取得
    node_details = []
    for node in workflow_run_nodes:
        node_details.append({
            "node_id": node.node_id,
            "status": node.status,
            "started_at": node.started_at,
            "finished_at": node.finished_at,
            "output_type": node.output_type,
            "output": node.output
        })

    return {
        "workflow_id": workflow_id,
        "workflow_run_id": workflow_run_id,
        "status": workflow_run.status,
        "started_at": workflow_run.started_at,
        "finished_at": workflow_run.finished_at if workflow_run.status in ["completed", "failed"] else None,
        "nodes": node_stats,
        "node_details": node_details
    }
