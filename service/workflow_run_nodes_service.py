# service/workflow_run_nodes_service.py

import uuid
from sqlalchemy.orm import Session
from sqlalchemy import select, desc
from fastapi import HTTPException, status
from models.workflow_run_nodes import WorkflowRunNode
from schemas.workflow_run_nodes import WorkflowRunNodeCreate, WorkflowRunNodeUpdate
from datetime import datetime, timezone
from typing import List, Optional

# Function IDs
FUNC_CREATE_WORKFLOW_RUN_NODE = "WRN001"
FUNC_UPDATE_WORKFLOW_RUN_NODE = "WRN002"
FUNC_GET_WORKFLOW_RUN_NODE = "WRN003"
FUNC_LIST_WORKFLOW_RUN_NODES = "WRN004"
FUNC_DELETE_WORKFLOW_RUN_NODE = "WRN005"
FUNC_LIST_WORKFLOW_RUN_NODES_BY_WORKFLOW_RUN = "WRN006"
FUNC_LIST_WORKFLOW_RUN_NODES_BY_NODE = "WRN007"

def create_workflow_run_node(db: Session, workflow_run_node_data: WorkflowRunNodeCreate) -> WorkflowRunNode:
    """
    新しいワークフロー実行ノードを作成する
    
    Args:
        db (Session): データベースセッション
        workflow_run_node_data (WorkflowRunNodeCreate): 作成するワークフロー実行ノードのデータ
        
    Returns:
        WorkflowRunNode: 作成されたワークフロー実行ノードのモデル
    """
    node_id = str(uuid.uuid4())
    
    db_workflow_run_node = WorkflowRunNode(
        id=node_id,
        user_id=workflow_run_node_data.user_id,
        workflow_run_id=workflow_run_node_data.workflow_run_id,
        node_id=workflow_run_node_data.node_id,
        node_ver=workflow_run_node_data.node_ver,
        status=workflow_run_node_data.status,
        params=workflow_run_node_data.params,
        memory=workflow_run_node_data.memory,
        flow_json=workflow_run_node_data.flow_json,
        output_type=workflow_run_node_data.output_type,
        output=workflow_run_node_data.output,
        started_at=datetime.now(timezone.utc),
        finished_at=workflow_run_node_data.finished_at,
        ip_addr=workflow_run_node_data.ip_addr
    )
    
    db.add(db_workflow_run_node)
    db.commit()
    db.refresh(db_workflow_run_node)
    
    return db_workflow_run_node

def update_workflow_run_node(db: Session, node_id: str, workflow_run_node_data: WorkflowRunNodeUpdate) -> Optional[WorkflowRunNode]:
    """
    既存のワークフロー実行ノードを更新する
    
    Args:
        db (Session): データベースセッション
        node_id (str): 更新するワークフロー実行ノードのID
        workflow_run_node_data (WorkflowRunNodeUpdate): 更新するデータ
        
    Returns:
        Optional[WorkflowRunNode]: 更新されたワークフロー実行ノードのモデル、存在しない場合はNone
    """
    db_workflow_run_node = db.query(WorkflowRunNode).filter(WorkflowRunNode.id == node_id).first()
    
    if not db_workflow_run_node:
        return None
    
    # 更新するフィールドを設定
    if workflow_run_node_data.status is not None:
        db_workflow_run_node.status = workflow_run_node_data.status
    
    if workflow_run_node_data.params is not None:
        db_workflow_run_node.params = workflow_run_node_data.params
    
    if workflow_run_node_data.memory is not None:
        db_workflow_run_node.memory = workflow_run_node_data.memory
    
    if workflow_run_node_data.flow_json is not None:
        db_workflow_run_node.flow_json = workflow_run_node_data.flow_json
    
    if workflow_run_node_data.output_type is not None:
        db_workflow_run_node.output_type = workflow_run_node_data.output_type
    
    if workflow_run_node_data.output is not None:
        db_workflow_run_node.output = workflow_run_node_data.output
    
    if workflow_run_node_data.finished_at is not None:
        db_workflow_run_node.finished_at = workflow_run_node_data.finished_at
    
    db_workflow_run_node.updated_at = datetime.now(timezone.utc)
    
    db.commit()
    db.refresh(db_workflow_run_node)
    
    return db_workflow_run_node

def get_workflow_run_node(db: Session, node_id: str) -> Optional[WorkflowRunNode]:
    """
    指定されたIDのワークフロー実行ノードを取得する
    
    Args:
        db (Session): データベースセッション
        node_id (str): 取得するワークフロー実行ノードのID
        
    Returns:
        Optional[WorkflowRunNode]: 取得したワークフロー実行ノードのモデル、存在しない場合はNone
    """
    return db.query(WorkflowRunNode).filter(WorkflowRunNode.id == node_id).first()

def get_workflow_run_nodes_by_user(db: Session, user_id: str, skip: int = 0, limit: int = 100) -> List[WorkflowRunNode]:
    """
    指定されたユーザーIDに関連するすべてのワークフロー実行ノードを取得する
    
    Args:
        db (Session): データベースセッション
        user_id (str): ユーザーID
        skip (int): スキップする件数
        limit (int): 取得する最大件数
        
    Returns:
        List[WorkflowRunNode]: ワークフロー実行ノードのリスト
    """
    return db.query(WorkflowRunNode).filter(WorkflowRunNode.user_id == user_id).order_by(desc(WorkflowRunNode.created_at)).offset(skip).limit(limit).all()

def get_workflow_run_nodes_by_workflow_run(db: Session, workflow_run_id: str, skip: int = 0, limit: int = 100) -> List[WorkflowRunNode]:
    """
    指定されたワークフロー実行IDに関連するすべてのワークフロー実行ノードを取得する
    
    Args:
        db (Session): データベースセッション
        workflow_run_id (str): ワークフロー実行ID
        skip (int): スキップする件数
        limit (int): 取得する最大件数
        
    Returns:
        List[WorkflowRunNode]: ワークフロー実行ノードのリスト
    """
    return db.query(WorkflowRunNode).filter(WorkflowRunNode.workflow_run_id == workflow_run_id).order_by(desc(WorkflowRunNode.created_at)).offset(skip).limit(limit).all()

def get_workflow_run_nodes_by_node(db: Session, node_id: str, skip: int = 0, limit: int = 100) -> List[WorkflowRunNode]:
    """
    指定されたノードIDに関連するすべてのワークフロー実行ノードを取得する
    
    Args:
        db (Session): データベースセッション
        node_id (str): ノードID
        skip (int): スキップする件数
        limit (int): 取得する最大件数
        
    Returns:
        List[WorkflowRunNode]: ワークフロー実行ノードのリスト
    """
    return db.query(WorkflowRunNode).filter(WorkflowRunNode.node_id == node_id).order_by(desc(WorkflowRunNode.created_at)).offset(skip).limit(limit).all()

def delete_workflow_run_node(db: Session, node_id: str) -> bool:
    """
    指定されたIDのワークフロー実行ノードを削除する
    
    Args:
        db (Session): データベースセッション
        node_id (str): 削除するワークフロー実行ノードのID
        
    Returns:
        bool: 削除に成功した場合はTrue、ワークフロー実行ノードが存在しない場合はFalse
    """
    db_workflow_run_node = db.query(WorkflowRunNode).filter(WorkflowRunNode.id == node_id).first()
    
    if not db_workflow_run_node:
        return False
    
    db.delete(db_workflow_run_node)
    db.commit()
    
    return True
