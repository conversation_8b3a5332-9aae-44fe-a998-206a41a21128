# service/workflow_runs_service.py

import uuid
from sqlalchemy.orm import Session
from sqlalchemy import select, desc
from fastapi import HTTPException, status
from models.workflow_runs import WorkflowRun
from schemas.workflow_runs import WorkflowRunCreate, WorkflowRunUpdate
from datetime import datetime, timezone
from typing import List, Optional

# Function IDs
FUNC_CREATE_WORKFLOW_RUN = "WFR001"
FUNC_UPDATE_WORKFLOW_RUN = "WFR002"
FUNC_GET_WORKFLOW_RUN = "WFR003"
FUNC_LIST_WORKFLOW_RUNS = "WFR004"
FUNC_DELETE_WORKFLOW_RUN = "WFR005"
FUNC_LIST_WORKFLOW_RUNS_BY_APP = "WFR006"

def create_workflow_run(db: Session, workflow_run_data: WorkflowRunCreate) -> WorkflowRun:
    """
    新しいワークフロー実行を作成する
    
    Args:
        db (Session): データベースセッション
        workflow_run_data (WorkflowRunCreate): 作成するワークフロー実行のデータ
        
    Returns:
        WorkflowRun: 作成されたワークフロー実行のモデル
    """
    workflow_run_id = str(uuid.uuid4())
    
    db_workflow_run = WorkflowRun(
        user_id=workflow_run_data.user_id,
        app_id=workflow_run_data.app_id,
        workflow_run_id=workflow_run_id,
        status=workflow_run_data.status,
        params=workflow_run_data.params,
        memory=workflow_run_data.memory,
        flow_json=workflow_run_data.flow_json,
        started_at=datetime.now(timezone.utc),
        finished_at=workflow_run_data.finished_at,
        ip_addr=workflow_run_data.ip_addr
    )
    
    db.add(db_workflow_run)
    db.commit()
    db.refresh(db_workflow_run)
    
    return db_workflow_run

def update_workflow_run(db: Session, workflow_run_id: str, workflow_run_data: WorkflowRunUpdate) -> Optional[WorkflowRun]:
    """
    既存のワークフロー実行を更新する
    
    Args:
        db (Session): データベースセッション
        workflow_run_id (str): 更新するワークフロー実行のID
        workflow_run_data (WorkflowRunUpdate): 更新するデータ
        
    Returns:
        Optional[WorkflowRun]: 更新されたワークフロー実行のモデル、存在しない場合はNone
    """
    db_workflow_run = db.query(WorkflowRun).filter(WorkflowRun.workflow_run_id == workflow_run_id).first()
    
    if not db_workflow_run:
        return None
    
    # 更新するフィールドを設定
    if workflow_run_data.status is not None:
        db_workflow_run.status = workflow_run_data.status
    
    if workflow_run_data.params is not None:
        db_workflow_run.params = workflow_run_data.params
    
    if workflow_run_data.memory is not None:
        db_workflow_run.memory = workflow_run_data.memory
    
    if workflow_run_data.flow_json is not None:
        db_workflow_run.flow_json = workflow_run_data.flow_json
    
    if workflow_run_data.finished_at is not None:
        db_workflow_run.finished_at = workflow_run_data.finished_at
    
    db_workflow_run.updated_at = datetime.now(timezone.utc)
    
    db.commit()
    db.refresh(db_workflow_run)
    
    return db_workflow_run

def get_workflow_run(db: Session, workflow_run_id: str) -> Optional[WorkflowRun]:
    """
    指定されたIDのワークフロー実行を取得する
    
    Args:
        db (Session): データベースセッション
        workflow_run_id (str): 取得するワークフロー実行のID
        
    Returns:
        Optional[WorkflowRun]: 取得したワークフロー実行のモデル、存在しない場合はNone
    """
    return db.query(WorkflowRun).filter(WorkflowRun.workflow_run_id == workflow_run_id).first()

def get_workflow_runs_by_user(db: Session, user_id: str, skip: int = 0, limit: int = 100) -> List[WorkflowRun]:
    """
    指定されたユーザーIDに関連するすべてのワークフロー実行を取得する
    
    Args:
        db (Session): データベースセッション
        user_id (str): ユーザーID
        skip (int): スキップする件数
        limit (int): 取得する最大件数
        
    Returns:
        List[WorkflowRun]: ワークフロー実行のリスト
    """
    return db.query(WorkflowRun).filter(WorkflowRun.user_id == user_id).order_by(desc(WorkflowRun.created_at)).offset(skip).limit(limit).all()

def get_workflow_runs_by_app(db: Session, app_id: str, skip: int = 0, limit: int = 100) -> List[WorkflowRun]:
    """
    指定されたアプリIDに関連するすべてのワークフロー実行を取得する
    
    Args:
        db (Session): データベースセッション
        app_id (str): アプリID
        skip (int): スキップする件数
        limit (int): 取得する最大件数
        
    Returns:
        List[WorkflowRun]: ワークフロー実行のリスト
    """
    return db.query(WorkflowRun).filter(WorkflowRun.app_id == app_id).order_by(desc(WorkflowRun.created_at)).offset(skip).limit(limit).all()

def delete_workflow_run(db: Session, workflow_run_id: str) -> bool:
    """
    指定されたIDのワークフロー実行を削除する
    
    Args:
        db (Session): データベースセッション
        workflow_run_id (str): 削除するワークフロー実行のID
        
    Returns:
        bool: 削除に成功した場合はTrue、ワークフロー実行が存在しない場合はFalse
    """
    db_workflow_run = db.query(WorkflowRun).filter(WorkflowRun.workflow_run_id == workflow_run_id).first()
    
    if not db_workflow_run:
        return False
    
    db.delete(db_workflow_run)
    db.commit()
    
    return True
