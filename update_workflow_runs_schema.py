#!/usr/bin/env python3
# update_workflow_runs_schema.py - 更新 workflow_runs 表结构

import pymysql
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def update_database_schema():
    """更新数据库表结构"""
    try:
        # 从环境变量解析数据库URL
        database_url = os.getenv('__DATABASE_URL', '')
        print(f"数据库URL: {database_url}")
        
        # 解析数据库URL: mysql+pymysql://user:password@host:port/database
        if database_url.startswith('mysql+pymysql://'):
            url_part = database_url.replace('mysql+pymysql://', '')
            user_pass, host_db = url_part.split('@')
            user, password = user_pass.split(':')
            host_port, database = host_db.split('/')
            host, port = host_port.split(':')
            
            print(f"连接到数据库: {host}:{port}/{database}")
            
            # 数据库连接配置
            connection = pymysql.connect(
                host=host,
                port=int(port),
                user=user,
                password=password,
                database=database,
                charset='utf8mb4'
            )
        else:
            raise ValueError("无效的数据库URL格式")
        
        with connection.cursor() as cursor:
            # 更新 workflow_runs 表，允许 finished_at 字段为 NULL
            sql = "ALTER TABLE workflow_runs MODIFY COLUMN finished_at DATETIME NULL;"
            print(f"执行SQL: {sql}")
            cursor.execute(sql)
            connection.commit()
            print("workflow_runs 表结构更新成功！")
            
    except Exception as e:
        print(f"更新数据库表结构失败: {e}")
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    update_database_schema()
