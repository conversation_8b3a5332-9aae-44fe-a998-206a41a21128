# utils/utils.py

import os
import json
import uuid
import datetime
from typing import Any, Dict, List, Optional
from pathlib import Path
import markdown
from utils.column_mappings import get_column_mapping
import asyncio

from utils.status import HTTP_404_NOT_FOUND  # status モジュールに定義されている場合
from fastapi import HTTPException
from sqlalchemy.orm import Session

def get_table_name(table_key: str) -> str:
    """
    指定されたテーブルキーに対応するテーブル名称（日本語表記）を返します。
    column_mappings.py で定義されたマッピングから __tablename__ の値を取得します。

    Args:
        table_key (str): カラムマッピングのキー（例: "customer_m"）

    Returns:
        str: テーブル名称。該当するマッピングが存在しない場合は空文字を返します。
    """
    mapping = get_column_mapping(table_key)
    return mapping.get("__tablename__", "")

def to_dict(obj):
    """SQLAlchemy オブジェクトまたは辞書をそのまま辞書に変換"""
    if isinstance(obj, dict):
        return obj
    elif hasattr(obj, "__table__"):
        return {column.name: getattr(obj, column.name) for column in obj.__table__.columns}
    else:
        raise ValueError(f"Unsupported type for to_dict conversion: {type(obj)}")

def db_to_markdown(data, table_name):
    """SQLAlchemy オブジェクトまたは辞書のリストをMarkdownテーブルに変換（複数件対応）"""
    if not data:
        return "No data available."

    # データを辞書リストに変換
    dict_data = [to_dict(row) for row in data]
    column_mapping = get_column_mapping(table_name)

    # ヘッダーを取得
    headers = list(dict_data[0].keys())
    display_headers = [column_mapping.get(h, h) for h in headers]

    # Markdown テーブルのヘッダー行を作成
    table = "| " + " | ".join(display_headers) + " |\n"
    table += "| " + " | ".join(["-" * len(h) for h in display_headers]) + " |\n"

    # 各行のデータを Markdown テーブルに追加
    for row in dict_data:
        row_values = []
        for h in headers:
            value = row.get(h, "")
            if value is None:
                value = ""
            row_values.append(str(value))
        table += "| " + " | ".join(row_values) + " |\n"

    markdown_text = f"""
---
{table}
---
"""
    return markdown_text

def create_folder(folder_path: str):
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)

def generate_uuid() -> str:
    """生成唯一的UUID字符串"""
    return str(uuid.uuid4())

def get_current_timestamp() -> int:
    """获取当前时间戳（秒）"""
    return int(datetime.datetime.now().timestamp())

def format_datetime(dt: Optional[datetime.datetime] = None, 
        format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化日期时间"""
    if dt is None:
        dt = datetime.datetime.now()
    return dt.strftime(format_str)

def load_json_file(file_path: str) -> Dict:
    """从文件加载JSON数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        raise Exception(f"Failed to load JSON file: {str(e)}")

def save_json_file(data: Dict, file_path: str) -> None:
    """保存数据到JSON文件"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        raise Exception(f"Failed to save JSON file: {str(e)}")

def ensure_file_exists(file_path: str) -> None:
    """确保文件存在，如果不存在则创建空文件"""
    Path(file_path).touch(exist_ok=True)

def get_file_extension(file_path: str) -> str:
    """获取文件扩展名"""
    return os.path.splitext(file_path)[1].lower()

def get_file_size(file_path: str) -> int:
    """获取文件大小（字节）"""
    return os.path.getsize(file_path)

def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """将列表分割成固定大小的块"""
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]

def remove_file_if_exists(file_path: str) -> None:
    """如果文件存在则删除"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
    except Exception as e:
        raise Exception(f"Failed to remove file: {str(e)}")
