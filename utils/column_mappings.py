# utils/column_mappings.py

# テーブルごとのカラム名と日本語表示のマッピング
column_mappings = {
    "user": {
        "__tablename__": "ユーザーマスタ",
        "id": "ユーザーID",
        "store_id": "店舗ID",
        "email": "メールアドレス",
        "username": "ユーザー名",
        "password": "パスワード",
        "nick_name": "ニックネーム",
        "avatar": "アバター",
        "role": "ロール",
        "user_roles": "ユーザーロール",
        "status": "ステータス",
        "platform": "プラットフォーム",
        "online": "オンライン状態",
        "last_connected_dtm": "最終接続日時",
        "ip_addr": "IPアドレス",
        "created_at": "作成日時",
        "updated_at": "更新日時"
    },
    "api_keys": {
       "__tablename__": "API keys",
        "user_id": "ユーザーID",
        "api_name": "API名",
        "api_key": "APIキー",
        "expiration": "有効期限",
        "memo": "メモ",
        "ip_addr": "IPアドレス",
        "created_at": "作成日時",
        "updated_at": "更新日時"
    },
    "mattermost_tokens": {
        "__tablename__": "mattermost tokens",
        "user_id": "ユーザーID",
        "api_token": "APIトークン",
        "api_name": "API名",
        "expiration": "有効期限",
        "url": "URL",
        "memo": "メモ",
        "status": "ステータス",
        "ip_addr": "IPアドレス",
        "created_at": "作成日時",
        "updated_at": "更新日時"
    },
    "meeting_summaries": {
        "__tablename__": "会議サマリ",
        "company_id": "会社ID",
        "user_id": "ユーザーID",
        "id": "ID",
        "file_id": "ファイルID",
        "meeting_name": "会議名",
        "recording_seconds": "録音時間（秒）",
        "language": "言語",
        "summary": "要約",
        "full_text": "全文",
        "map_id": "マップID",
        "status_code": "ステータスコード",
        "is_delete": "削除フラグ",
        "ip_addr": "IPアドレス",
        "created_at": "作成日時",
        "updated_at": "更新日時"
    },
    "apps": {
        "__tablename__": "アプリ",
        "user_id": "ユーザーID",
        "app_id": "アプリID",
        "app_name": "アプリ名称",
        "app_description": "アプリ説明",
        "app_icon_file": "アプリアイコン",
        "tag": "タグ",
        "flow_json": "フローJSON",
        "ip_addr": "IPアドレス",
        "created_at": "作成日時",
        "updated_at": "更新日時"
    },
    "workflow_runs": {
        "__tablename__": "ワークフロー実行",
        "user_id": "ユーザーID",
        "app_id": "アプリID",
        "workflow_run_id": "ワークフロー実行ID",
        "status": "実行状態",
        "params": "パラメーター",
        "memory": "メモリ",
        "flow_json": "フローJSON",
        "started_at": "開始時刻",
        "finished_at": "終了時刻",
        "ip_addr": "IPアドレス",
        "created_at": "作成日時",
        "updated_at": "更新日時"
    },
    "workflow_run_nodes": {
        "__tablename__": "ワークフロー実行ノード",
        "id": "ID",
        "user_id": "ユーザーID",
        "workflow_run_id": "ワークフロー実行ID",
        "node_id": "ノードID",
        "node_ver": "ノードVer",
        "status": "実行状態",
        "params": "パラメーター",
        "memory": "メモリ",
        "flow_json": "フローJSON",
        "output_type": "レスポンスtype",
        "output": "レスポンス",
        "started_at": "開始時刻",
        "finished_at": "終了時刻",
        "ip_addr": "IPアドレス",
        "created_at": "作成日時",
        "updated_at": "更新日時"
    },
    "variables": {
        "__tablename__": "変数",
        "id": "ID",
        "user_id": "ユーザーID",
        "workflow_run_id": "ワークフロー実行ID",
        "var_key": "変数ID",
        "var_val": "変数値",
        "ip_addr": "IPアドレス",
        "created_at": "作成日時",
        "updated_at": "更新日時"
    },
    "customer_m": {
        "__tablename__": "顧客マスタ",
        "user_id": "ユーザーID",
        "customer_id": "顧客ID",
        "customer_name": "顧客名",
        "customer_furigana": "顧客名(ふりがな)",
        "corp_type": "事業所種別",
        "foreign_type": "外国区分",
        "postal_code": "郵便番号",
        "address": "住所",
        "phone": "電話番号",
        "invoice_code": "適格請求書発行事業者番号",
        "official_name": "正式名称（帳票表示）",
        "official_name_kana": "正式名称（カナ）",
        "customer_official_name": "敬称",
        "customer_staff_name": "顧客担当者-名",
        "customer_staff_official_name": "顧客担当者-敬称",
        "customer_staff_department": "顧客担当者-部署",
        "customer_staff_email": "顧客担当者-メールアドレス",
        "customer_staff_phone": "顧客担当者-電話番号",
        "staff_name": "自社担当者",
        "customer_sales_role": "顧客役割-販売",
        "customer_purchase_role": "顧客役割-仕入",
        "claim_tax_status": "請求-税区分",
        "claim_send_status": "請求-送付方法",
        "bank_name": "顧客口座-銀行名",
        "bank_kana": "顧客口座-銀行名カナ",
        "bank_no": "顧客口座-銀行番号",
        "branch_name": "顧客口座-支店名",
        "branch_kana": "顧客口座-支店名カナ",
        "branch_no": "顧客口座-支店番号",
        "account_type": "顧客口座-口座種別",
        "account_no": "顧客口座-口座番号",
        "account_name": "顧客口座-受取人名",
        "account_name_kana": "顧客口座-受取人名カナ",
        "is_delete": "削除",
        "ip_addr": "IPアドレス",
        "created_at": "作成タイムスタンプ",
        "updated_at": "更新タイムスタンプ"
    }
}

def get_column_mapping(table_name: str) -> dict:
    """指定されたテーブルのカラム名マッピングを取得"""
    return column_mappings.get(table_name, {})