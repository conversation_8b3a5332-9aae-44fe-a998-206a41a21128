import React, { useState, useRef, useEffect } from 'react'; // 导入 useRef 和 useEffect
import './css/top.css';
// 导入 Top 组件中实际使用的图标
import { FaAngleDown } from 'react-icons/fa';
import { FcApprove, FcMindMap, FcVoicePresentation, FcSupport, FcInfo, FcSettings, FcExport } from 'react-icons/fc';
// 导入导航所需的组件
import { useNavigate, useLocation } from 'react-router-dom';
// 导入认证服务
import { logout } from '../services/authService';

const Top = () => {
  const [isAccountMenuOpen, setIsAccountMenuOpen] = useState(false);
  const accountMenuRef = useRef<HTMLDivElement>(null); // 账户菜单 div 的引用
  const accountButtonRef = useRef<HTMLButtonElement>(null); // 账户按钮的引用
  const navigate = useNavigate();
  const location = useLocation();

  const toggleAccountMenu = () => {
    setIsAccountMenuOpen(!isAccountMenuOpen);
  };

  const navigateTo = (path: string) => {
    navigate(path);
  };

  // Effect to handle clicks outside the menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      // 检查点击的目标是否在菜单内部 或 按钮内部
      // 如果不在两者内部，则关闭菜单
      if (
        accountMenuRef.current && !accountMenuRef.current.contains(target) &&
        accountButtonRef.current && !accountButtonRef.current.contains(target)
      ) {
        setIsAccountMenuOpen(false);
      }
    };

    // Add event listener when the menu is open
    if (isAccountMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    // Cleanup function to remove the event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isAccountMenuOpen]); // 依赖项数组，当 isAccountMenuOpen 改变时重新运行 effect

  return (
    <div className="header">
      <div className="leftSection">
        <div className="workspaceSelector">
          <span>AfireAI's Workspace</span>
          <FaAngleDown />
        </div>
      </div>

      <div className="centerSection">
        <button
          className={`navButton ${location.pathname.includes('/app') ? 'active' : ''}`}
          onClick={() => navigateTo('/apps')}
        >
          <FcApprove className="icon" />
          <span>Agent</span>
        </button>
        <button
          className={`navButton ${location.pathname.includes('/datasets') ? 'active' : ''}`}
          onClick={() => navigateTo('/datasets')}
        >
          <FcMindMap className="icon" />
          <span>ナレッジ</span>
        </button>
        <button
          className={`navButton ${location.pathname.includes('/tools') ? 'active' : ''}`}
          onClick={() => navigateTo('/tools')}
        >
          <FcSupport className="icon" />
          <span>ツール</span>
        </button>
      </div>

      <div className="rightSection">
        <div className="accountInfo">
          {/* 将 ref 关联到按钮 */}
          <button
            ref={accountButtonRef}
            className="accountButton"
            onClick={toggleAccountMenu}
          >
            <div className="avatar">D</div>
          </button>
          {/* 条件渲染菜单 */}
          {isAccountMenuOpen && (
            // 将 ref 关联到菜单容器 div
            <div ref={accountMenuRef} className="accountMenu">
              <div className="userInfo">
                <div className="avatarLarge">D</div>
                <div>
                  <div>Gjj</div>
                  <div className="email"><EMAIL></div>
                </div>
              </div>
              {/* 菜单项 */}
              {/* 点击菜单项时也可以选择关闭菜单，这里未实现，如果需要可以在onClick里加 setIsAccountMenuOpen(false) */}
              <button onClick={() => navigateTo('/account')} className="menuItem"><FcVoicePresentation className="menuIcon" /> アカウント</button>
              <button onClick={() => navigateTo('/settings')} className="menuItem"><FcSettings className="menuIcon" /> 設定</button>
              <button onClick={() => navigateTo('/about')} className="menuItem"><FcInfo className="menuIcon" /> AfireAIについて <span className="version">1.3.1</span></button>
              <hr className="divider" />
              <button
                onClick={() => {
                  logout();
                  setIsAccountMenuOpen(false);
                  navigateTo('/signin');
                }}
                className="menuItem"
              >
                <FcExport className="menuIcon" /> ログアウト
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Top;