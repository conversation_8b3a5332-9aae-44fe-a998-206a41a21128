import React, { useState, useRef } from 'react';
import { FaTimes, FaRobot } from 'react-icons/fa';
import './css/CreateAppModal.css';
import { showError } from '../utils/toast';

interface CreateAppModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateApp: (appName: string, appDescription: string, appIconFile: string, tag: string) => void;
}

const CreateAppModal: React.FC<CreateAppModalProps> = ({ isOpen, onClose, onCreateApp }) => {
  const [appName, setAppName] = useState('');
  const [appDescription, setAppDescription] = useState('');
  const [appIcon, setAppIcon] = useState<string>('');
  const [appTag, setAppTag] = useState<string>('workflow');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 如果弹窗未打开，则不渲染
  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // 表单验证
    if (!appName.trim()) {
      showError('アプリ名を入力してください');
      return;
    }

    // 使用默认图标如果没有选择
    const iconUrl = appIcon || 'https://www.istart.co.jp/logo.png';

    // 使用默认描述如果没有输入
    const description = appDescription.trim() || 'ワークフローアプリケーション';

    console.log('Form submitted with:', {
      appName,
      appDescription: description,
      iconUrl,
      appTag
    });

    // 调用父组件的创建方法
    onCreateApp(appName, description, iconUrl, appTag);
  };

  const handleIconSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      // 在实际应用中，这里应该上传文件到服务器并获取URL
      // 这里我们只是模拟一个URL
      setAppIcon(URL.createObjectURL(files[0]));
    }
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="modal-overlay">
      <div className="create-app-modal">
        <div className="modal-header">
          <h2>アプリを作成する</h2>
          <button className="close-button" onClick={onClose}>
            <FaTimes />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="app-icon-section">
            <div className="app-icon-container" onClick={triggerFileInput}>
              {appIcon ? (
                <img src={appIcon} alt="App Icon" className="app-icon-preview" />
              ) : (
                <div className="app-icon-placeholder">
                  <FaRobot size={40} />
                </div>
              )}
            </div>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleIconSelect}
              accept="image/*"
              style={{ display: 'none' }}
            />
          </div>

          <div className="form-group">
            <input
              type="text"
              placeholder="アプリ名を入力してください"
              value={appName}
              onChange={(e) => setAppName(e.target.value)}
              className="app-name-input"
            />
          </div>

          <div className="form-group">
            <label>説明 (任意)</label>
            <textarea
              placeholder="アプリの説明を入力してください"
              value={appDescription}
              onChange={(e) => setAppDescription(e.target.value)}
              className="app-description-input"
            />
          </div>

          <div className="form-group">
            <label>タグ:workflow</label>
          </div>

          <div className="modal-footer">
            <button type="button" className="cancel-button" onClick={onClose}>
              キャンセル
            </button>
            <button type="submit" className="create-button">
              アプリを作成する
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateAppModal;
