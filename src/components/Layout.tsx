import React from 'react';
import { useLocation } from 'react-router-dom';
import Top from './top';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  
  // 检查当前路径是否为登录页面
  const isAuthPage = location.pathname === '/signin';
  
  return (
    <div className="app-container">
      {/* 只在非登录页面显示顶部导航栏 */}
      {!isAuthPage && (
        <div className="header-container">
          <Top />
        </div>
      )}
      {children}
    </div>
  );
};

export default Layout;
