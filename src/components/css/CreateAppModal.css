.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.create-app-modal {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    width: 500px;
    max-width: 90%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.modal-header h2 {
    font-size: 18px;
    margin: 0;
    font-weight: 500;
}

.close-button {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
}

.app-icon-section {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.app-icon-container {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background-color: #f5f7f9;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    overflow: hidden;
    border: 1px dashed #ccc;
}

.app-icon-placeholder {
    color: #aaa;
}

.app-icon-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
}

.app-name-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 16px;
    background-color: #f5f7f9;
}

.app-description-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    min-height: 100px;
    resize: vertical;
    background-color: #f5f7f9;
}

.app-tag-select {
    width: 100%;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    background-color: #f5f7f9;
    appearance: menulist;
    cursor: pointer;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
}

.cancel-button {
    padding: 10px 16px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: white;
    font-size: 14px;
    cursor: pointer;
}

.create-button {
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    background-color: #4a90e2;
    color: white;
    font-size: 14px;
    cursor: pointer;
}

.create-button:hover {
    background-color: #3a7bc8;
}