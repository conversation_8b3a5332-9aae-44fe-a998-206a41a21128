.button {
  background-color: #ffffff; /* White background */
  color: #0d6efd; /* Blue text, like "実行" */
  border: 1px solid #dee2e6; /* Light gray border */
  padding: 8px 16px; /* More spacious padding, like "実行" */
  border-radius: 8px; /* More rounded corners, as per image */
  cursor: pointer;
  margin-left: 8px;
  font-weight: 500; /* Medium font weight for emphasis */
  text-align: center; /* Ensure text is centered */
  vertical-align: middle; /* Align with other inline elements */
  user-select: none; /* Prevent text selection on click */
  transition: color 0.15s ease-in-out, 
  background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.button:hover {
  background-color: #f0f0f0; /* Light gray background on hover */
  color: #0056b3; /* Darker blue text on hover */
  border-color: #ced4da; /* Slightly darker gray border on hover */
}

.button-row {
  display: flex;
  gap: 2px;
  margin-bottom: 16px;
}

.icon-small {
  font-size: 1em;
  margin-right: 2px;
}
