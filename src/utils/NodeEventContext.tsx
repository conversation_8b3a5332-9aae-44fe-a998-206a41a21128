import React, { createContext, useContext, useState, ReactNode } from 'react';

// 定义节点类型
interface NodeData {
  id: string;
  data: any;
  type: string;
}

// 定义上下文类型
interface NodeEventContextType {
  selectedNode: NodeData | null;
  selectNode: (node: NodeData) => void;
  clearSelectedNode: () => void;
}

// 创建上下文
const NodeEventContext = createContext<NodeEventContextType | undefined>(undefined);

// 创建Provider组件
export const NodeEventProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [selectedNode, setSelectedNode] = useState<NodeData | null>(null);

  const selectNode = (node: NodeData) => {
    console.log('NodeEventContext: selectNode called with node:', node);
    setSelectedNode(node);
    console.log('NodeEventContext: selectedNode after update:', node);
  };

  const clearSelectedNode = () => {
    setSelectedNode(null);
  };

  return (
    <NodeEventContext.Provider value={{ selectedNode, selectNode, clearSelectedNode }}>
      {children}
    </NodeEventContext.Provider>
  );
};

// 创建自定义Hook
export const useNodeEvents = () => {
  const context = useContext(NodeEventContext);
  if (context === undefined) {
    throw new Error('useNodeEvents must be used within a NodeEventProvider');
  }
  return context;
};
