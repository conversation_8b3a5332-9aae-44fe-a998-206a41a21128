// main.tsx
import React, { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css'; // 确保全局 CSS 被引入
import AppRoutes from './router'; // 导入新的路由配置
import Layout from './components/Layout';
import { BrowserRouter } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// 导入工作流样式
import './page/apps/css/Workflow.css';

const rootElement = document.getElementById('root');
if (!rootElement) throw new Error('Failed to find the root element');
createRoot(rootElement).render(
  <StrictMode>
    <BrowserRouter>
      <Layout>
        <AppRoutes />
        <ToastContainer
          position="top-right"
          autoClose={3000}
          hideProgressBar={false}
          newestOnTop
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
        />
      </Layout>
    </BrowserRouter>
  </StrictMode>,
);