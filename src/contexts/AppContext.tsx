import React, { createContext, useState, useContext, ReactNode } from 'react';
import { AppResponse } from '../services/appService';

// 定义上下文类型
interface AppContextType {
  appInfo: AppResponse | null;
  setAppInfo: (info: AppResponse | null) => void;
  flowJson: string;
  setFlowJson: (json: string) => void;
}

// 创建上下文
const AppContext = createContext<AppContextType | undefined>(undefined);

// 上下文提供者组件
export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [appInfo, setAppInfo] = useState<AppResponse | null>(null);
  const [flowJson, setFlowJson] = useState<string>('');

  return (
    <AppContext.Provider value={{ appInfo, setAppInfo, flowJson, setFlowJson }}>
      {children}
    </AppContext.Provider>
  );
};

// 自定义钩子，用于访问上下文
export const useAppContext = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};
