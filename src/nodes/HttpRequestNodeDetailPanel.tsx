import React, { useState } from 'react';
import { useReactFlow } from '@xyflow/react';
import { FaGlobe, FaChevronDown, FaChevronUp, FaPlus, FaTrash } from 'react-icons/fa';
import './css/HttpRequestNodeDetailPanel.css';

// HTTP方法选项
const HTTP_METHODS = ['GET', 'POST', 'HEAD', 'PATCH', 'PUT', 'DELETE'];

// 请求体类型选项
const BODY_TYPES = ['none', 'form-data', 'x-www-form-urlencoded', 'JSON', 'raw', 'binary'];

interface HttpRequestNodeDetailPanelProps {
  node: any;
  onClose: () => void;
}

const HttpRequestNodeDetailPanel: React.FC<HttpRequestNodeDetailPanelProps> = ({ node, onClose }) => {
  console.log('Rendering HttpRequestNodeDetailPanel with node:', node);
  console.log('Node type:', node?.type);
  console.log('Node id:', node?.id);

  // 如果node为空，则不渲染
  if (!node) {
    console.error('HttpRequestNodeDetailPanel: node is null or undefined');
    return null;
  }

  const { setNodes } = useReactFlow();
  const nodeId = node.id;
  const data = node.data;

  // 状态
  const [description, setDescription] = useState(data?.description || '');
  const [method, setMethod] = useState(data?.method || 'GET');
  const [url, setUrl] = useState(data?.url || '');
  const [headers, setHeaders] = useState(data?.headers || [{ key: '', value: '' }]);
  const [params, setParams] = useState(data?.params || [{ key: '', value: '' }]);
  const [bodyType, setBodyType] = useState(data?.bodyType || 'binary');
  const [timeout, setTimeout] = useState(data?.timeout || 30);

  // 展开/折叠状态
  const [timeoutExpanded, setTimeoutExpanded] = useState(false);
  const [outputExpanded, setOutputExpanded] = useState(false);

  // 保存节点数据
  const saveNodeData = () => {
    setNodes(nodes =>
      nodes.map(node => {
        if (node.id === nodeId) {
          return {
            ...node,
            data: {
              ...node.data,
              label: 'HTTPリクエスト',
              description,
              method,
              url,
              headers,
              params,
              bodyType,
              timeout
            }
          };
        }
        return node;
      })
    );
  };

  // 添加新的头部
  const addHeader = () => {
    setHeaders([...headers, { key: '', value: '' }]);
    saveNodeData();
  };

  // 删除头部
  const removeHeader = (index: number) => {
    const newHeaders = [...headers];
    newHeaders.splice(index, 1);
    setHeaders(newHeaders);
    saveNodeData();
  };

  // 更新头部
  const updateHeader = (index: number, field: 'key' | 'value', value: string) => {
    const newHeaders = [...headers];
    newHeaders[index][field] = value;
    setHeaders(newHeaders);
    saveNodeData();
  };

  // 添加新的参数
  const addParam = () => {
    setParams([...params, { key: '', value: '' }]);
    saveNodeData();
  };

  // 删除参数
  const removeParam = (index: number) => {
    const newParams = [...params];
    newParams.splice(index, 1);
    setParams(newParams);
    saveNodeData();
  };

  // 更新参数
  const updateParam = (index: number, field: 'key' | 'value', value: string) => {
    const newParams = [...params];
    newParams[index][field] = value;
    setParams(newParams);
    saveNodeData();
  };

  return (
    <div className="http-request-node-detail-panel" style={{position: 'absolute', top: 0, right: 0, width: '400px', height: '100%', backgroundColor: 'white', zIndex: 1000, boxShadow: '-2px 0 10px rgba(0, 0, 0, 0.1)'}}>
      <div className="node-detail-header">
        <div className="node-detail-title">
          <div className="node-icon">
            <FaGlobe />
          </div>
          <h2>HTTPリクエスト</h2>
        </div>
        <div className="node-detail-actions">
          <button className="icon-button" onClick={onClose}>×</button>
        </div>
      </div>

      <div className="detail-panel-content">
        <div className="form-group">
          <input
            type="text"
            placeholder="説明を追加..."
            value={description}
            onChange={(e) => {
              setDescription(e.target.value);
              saveNodeData();
            }}
            className="description-input"
          />
        </div>

        <div className="form-group required">
          <label>API *</label>
          <div className="api-input-group">
            <select
              value={method}
              onChange={(e) => {
                setMethod(e.target.value);
                saveNodeData();
              }}
              className="method-select"
            >
              {HTTP_METHODS.map(m => (
                <option key={m} value={m}>{m}</option>
              ))}
            </select>

            <input
              type="text"
              placeholder={"URLを入力（変数使用時は\"/\"を入力）"}
              value={url}
              onChange={(e) => {
                setUrl(e.target.value);
                saveNodeData();
              }}
              className="url-input"
            />
          </div>
        </div>

        <div className="form-section">
          <h3>ヘッダー</h3>
          <div className="key-value-list">
            {headers.map((header: {key: string, value: string}, index: number) => (
              <div key={index} className="key-value-row">
                <input
                  type="text"
                  placeholder="キー"
                  value={header.key}
                  onChange={(e) => updateHeader(index, 'key', e.target.value)}
                  className="key-input"
                />
                <input
                  type="text"
                  placeholder="値"
                  value={header.value}
                  onChange={(e) => updateHeader(index, 'value', e.target.value)}
                  className="value-input"
                />
                <button
                  className="remove-button"
                  onClick={() => removeHeader(index)}
                >
                  <FaTrash />
                </button>
              </div>
            ))}
            <button className="add-button" onClick={addHeader}>
              <FaPlus /> {`変数を挿入するには"/"を...`}
            </button>
          </div>
        </div>

        <div className="form-section">
          <h3>パラメータ</h3>
          <div className="key-value-list">
            {params.map((param: {key: string, value: string}, index: number) => (
              <div key={index} className="key-value-row">
                <input
                  type="text"
                  placeholder="キー"
                  value={param.key}
                  onChange={(e) => updateParam(index, 'key', e.target.value)}
                  className="key-input"
                />
                <input
                  type="text"
                  placeholder="値"
                  value={param.value}
                  onChange={(e) => updateParam(index, 'value', e.target.value)}
                  className="value-input"
                />
                <button
                  className="remove-button"
                  onClick={() => removeParam(index)}
                >
                  <FaTrash />
                </button>
              </div>
            ))}
            <button className="add-button" onClick={addParam}>
              <FaPlus /> {`変数を挿入するには"/"を...`}
            </button>
          </div>
        </div>

        <div className="form-section required">
          <h3>ボディ *</h3>
          <div className="body-type-options">
            {BODY_TYPES.map(type => (
              <label key={type} className="body-type-option">
                <input
                  type="radio"
                  name="bodyType"
                  value={type}
                  checked={bodyType === type}
                  onChange={() => {
                    setBodyType(type);
                    saveNodeData();
                  }}
                />
                <span>{type}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="detail-section">
          <div className="section-header" onClick={() => setTimeoutExpanded(!timeoutExpanded)}>
            <h3>タイムアウト設定</h3>
            {timeoutExpanded ? <FaChevronUp /> : <FaChevronDown />}
          </div>
          {timeoutExpanded && (
            <div className="section-content">
              <div className="form-group">
                <label>接続タイムアウト (秒)</label>
                <input
                  type="number"
                  value={timeout}
                  onChange={(e) => {
                    setTimeout(Number(e.target.value));
                    saveNodeData();
                  }}
                  className="timeout-input"
                />
              </div>
            </div>
          )}
        </div>

        <div className="detail-section">
          <div className="section-header" onClick={() => setOutputExpanded(!outputExpanded)}>
            <h3>出力変数</h3>
            {outputExpanded ? <FaChevronUp /> : <FaChevronDown />}
          </div>
          {outputExpanded && (
            <div className="section-content output-variables">
              <div className="output-variable">
                <div className="variable-name">body</div>
                <div className="variable-type">string</div>
                <div className="variable-description">レスポンスコンテンツ</div>
              </div>
              <div className="output-variable">
                <div className="variable-name">status_code</div>
                <div className="variable-type">number</div>
                <div className="variable-description">レスポンスステータスコード</div>
              </div>
              <div className="output-variable">
                <div className="variable-name">headers</div>
                <div className="variable-type">object</div>
                <div className="variable-description">レスポンスヘッダ (JSON)</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HttpRequestNodeDetailPanel;
