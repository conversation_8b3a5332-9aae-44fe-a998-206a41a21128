import React, { useState } from 'react';
import { FaTimes, FaPlus, FaBook, FaEllipsisH } from 'react-icons/fa';
import { IoHomeSharp } from 'react-icons/io5';
import './css/NodeDetailPanel.css';
import EnvVariableModal from './EnvVariableModal';

interface StartNodeDetailPanelProps {
  node: any;
  onClose: () => void;
}

const StartNodeDetailPanel: React.FC<StartNodeDetailPanelProps> = ({ node, onClose }) => {
  const [description, setDescription] = useState<string>('');
  const [showEnvModal, setShowEnvModal] = useState(false);
  const [customVariables, setCustomVariables] = useState<Array<{id: string, type: string}>>([]);

  // 系统变量数据
  const systemVariables = [
    { id: 'sys.files', type: 'Array[File]', legacy: true },
    { id: 'sys.user_id', type: 'String' },
    { id: 'sys.app_id', type: 'String' },
    { id: 'sys.workflow_id', type: 'String' },
    { id: 'sys.workflow_run_id', type: 'String' },
  ];

  const handleAddVariable = (variable: { name: string; type: string; value: string }) => {
    setCustomVariables([...customVariables, { id: variable.name, type: variable.type }]);
  };

  return (
    <div className="node-detail-panel">
      <div className="node-detail-header">
        <div className="node-detail-title">
          <div className="node-icon">
            <IoHomeSharp />
          </div>
          <h2>{node.data.label || '開始'}</h2>
        </div>
        <div className="node-detail-actions">
          <button className="icon-button" onClick={onClose}>
            <FaTimes />
          </button>
        </div>
      </div>

      <div className="node-detail-content">
        <div className="description-section">
          <textarea
            placeholder="説明を追加..."
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="description-input"
          />
        </div>

        <div className="input-fields-section">
          <div className="section-header">
            <h3>入力フィールド</h3>
            <button className="add-field-button" onClick={() => setShowEnvModal(true)}>
              <FaPlus />
            </button>
          </div>
          <div className="system-variables">
            {/* 自定义变量 */}
            {customVariables.map((variable) => (
              <div key={variable.id} className="variable-item">
                <div className="variable-name">
                  <span className="variable-tag">{'{x}'}</span> {variable.id}
                </div>
                <div className="variable-type">{variable.type}</div>
              </div>
            ))}

            {/* 系统变量 */}
            {systemVariables.map((variable) => (
              <div key={variable.id} className="variable-item">
                <div className="variable-name">
                  <span className="variable-tag">{'{x}'}</span> {variable.id}
                  {variable.legacy && <span className="legacy-tag">LEGACY</span>}
                </div>
                <div className="variable-type">{variable.type}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 环境变量弹出框 */}
      {showEnvModal && (
        <EnvVariableModal
          onClose={() => setShowEnvModal(false)}
          onSave={handleAddVariable}
        />
      )}
    </div>
  );
};

export default StartNodeDetailPanel;
