import React, { memo, CSSProperties } from 'react';
import { Handle, Position, NodeProps, Connection } from '@xyflow/react';

const DEFAULT_HANDLE_STYLE: CSSProperties = {
  width: 10,
  height: 10,
  bottom: -5,
};

interface ColorSelectorNodeData {
  color: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

interface ColorSelectorNodeProps extends NodeProps<ColorSelectorNodeData> {
  isConnectable: boolean;
}

const ColorSelectorNode: React.FC<ColorSelectorNodeProps> = memo(({ data, isConnectable }) => {
  return (
    <>
      <Handle
        type="target"
        position={Position.Left}
        onConnect={(params: Connection) => console.log('handle onConnect', params)}
        isConnectable={isConnectable}
      />
      <div style={{ border: '1px solid #ccc', padding: '10px', borderRadius: '8px', backgroundColor: '#fff' }}>
        Custom Color Picker Node: <strong>{data.color}</strong>
        <input
          className="nodrag"
          type="color"
          onChange={data.onChange}
          defaultValue={data.color}
          style={{ display: 'block', marginTop: '8px' }}
        />
      </div>
      <Handle
        type="source"
        position={Position.Right}
        style={{ ...DEFAULT_HANDLE_STYLE, left: '95%', top: '70%', background: 'red' }}
        id="red"
        isConnectable={isConnectable}
      />
      <Handle
        type="source"
        position={Position.Right}
        style={{ ...DEFAULT_HANDLE_STYLE, left: '95%', top: '30%', background: 'blue' }}
        id="blue"
        isConnectable={isConnectable}
      />
    </>
  );
});

export default ColorSelectorNode;
