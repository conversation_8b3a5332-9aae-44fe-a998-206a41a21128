import { memo } from 'react';
import { Position, Handle, useReactFlow, type NodeProps, type Node } from '@xyflow/react';
import { FaCode } from 'react-icons/fa';
import './css/CodeNode.css';

// 导入全局事件总线
import { useNodeEvents } from '../utils/NodeEventContext';

interface CodeNodeData {
  label?: string;
  language?: string;
}

function CodeNode({ id, data }: NodeProps<Node<CodeNodeData>>) {
  const { updateNodeData } = useReactFlow();
  const { selectNode } = useNodeEvents();

  const handleNodeClick = () => {
    // 触发节点选择事件，传递当前节点信息
    selectNode({ id, data, type: 'codeNode' });
  };

  return (
    <div className="code-node" onClick={handleNodeClick}>
      <div className="code-node-header">
        <div className="code-node-icon">
          <FaCode />
        </div>
        <div className="code-node-title">
          {data.label || 'コード実行'}
        </div>
      </div>
      
      <Handle
        type="target"
        position={Position.Left}
        style={{
          background: '#4a90e2',
          width: '8px',
          height: '8px',
          top: '50%',
          border: '1px solid white'
        }}
      />
      
      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: '#4a90e2',
          width: '8px',
          height: '8px',
          top: '50%',
          border: '1px solid white'
        }}
      />
    </div>
  );
}

export default memo(CodeNode);
