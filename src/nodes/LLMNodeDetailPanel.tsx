import React, { useState } from 'react';
import { FaTimes, FaBook, FaEllipsisH, FaQuestionCircle, FaPlus, FaChevronDown, FaExpand, FaRegClipboard } from 'react-icons/fa';
import { FaRobot } from 'react-icons/fa';
import { BiSlider } from 'react-icons/bi';
import { useReactFlow } from '@xyflow/react';
import './css/NodeDetailPanel.css';
import './css/LLMNodeDetailPanel.css';

interface LLMNodeDetailPanelProps {
  node: any;
  onClose: () => void;
}

const LLMNodeDetailPanel: React.FC<LLMNodeDetailPanelProps> = ({ node, onClose }) => {
  const { setNodes } = useReactFlow();
  const [description, setDescription] = useState<string>('');
  const [selectedModel, setSelectedModel] = useState<string>(node.data.model || 'gpt-3.5-turbo');
  const [showModelDropdown, setShowModelDropdown] = useState<boolean>(false);
  const [visionEnabled, setVisionEnabled] = useState<boolean>(false);
  const [structuredOutput, setStructuredOutput] = useState<boolean>(false);
  const [retryOnFailure, setRetryOnFailure] = useState<boolean>(false);
  
  // 可用的模型列表
  const availableModels = [
    { id: 'gpt-3.5-turbo', name: 'gpt-3.5-turbo', type: 'CHAT' },
    { id: 'gpt-4', name: 'gpt-4', type: 'CHAT' },
    { id: 'gpt-4-turbo', name: 'gpt-4-turbo', type: 'CHAT' },
    { id: 'claude-3-opus', name: 'claude-3-opus', type: 'CHAT' },
    { id: 'claude-3-sonnet', name: 'claude-3-sonnet', type: 'CHAT' },
    { id: 'claude-3-haiku', name: 'claude-3-haiku', type: 'CHAT' },
  ];

  // 更新节点数据
  const updateNodeData = (data: any) => {
    setNodes((nodes) =>
      nodes.map((n) => {
        if (n.id === node.id) {
          return {
            ...n,
            data: {
              ...n.data,
              ...data,
            },
          };
        }
        return n;
      })
    );
  };

  // 选择模型
  const handleModelSelect = (modelId: string) => {
    setSelectedModel(modelId);
    updateNodeData({ model: modelId });
    setShowModelDropdown(false);
  };

  return (
    <div className="node-detail-panel">
      <div className="node-detail-header">
        <div className="node-detail-title">
          <div className="node-icon llm-node-icon-color">
            <FaRobot />
          </div>
          <h2>{node.data.label || 'LLM'}</h2>
        </div>
        <div className="node-detail-actions">
          <button className="icon-button" onClick={onClose}>
            <FaTimes />
          </button>
        </div>
      </div>

      <div className="node-detail-content">
        <div className="description-section">
          <textarea
            placeholder="説明を追加..."
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="description-input"
          />
        </div>

        <div className="llm-model-section">
          <div className="section-header">
            <h3>AIモデル <span className="required-mark">*</span></h3>
          </div>
          
          <div className="model-selector" onClick={() => setShowModelDropdown(!showModelDropdown)}>
            <div className="selected-model">
              <div className="model-icon">
                <img src="/openai-logo.svg" alt="Model" width="16" height="16" />
              </div>
              <div className="model-name">{selectedModel}</div>
              <div className="model-tag">CHAT</div>
            </div>
            <div className="model-actions">
              <BiSlider />
              <FaChevronDown />
            </div>
          </div>
          
          {showModelDropdown && (
            <div className="model-dropdown">
              {availableModels.map((model) => (
                <div 
                  key={model.id} 
                  className={`model-option ${selectedModel === model.id ? 'selected' : ''}`}
                  onClick={() => handleModelSelect(model.id)}
                >
                  <div className="model-icon">
                    <img src="/openai-logo.svg" alt="Model" width="16" height="16" />
                  </div>
                  <div className="model-name">{model.name}</div>
                  <div className="model-tag">{model.type}</div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="context-section">
          <div className="section-header">
            <h3>コンテキスト <FaQuestionCircle className="help-icon" /></h3>
          </div>
          
          <div className="context-variable-button">
            <FaRegClipboard /> 変数値を設定
          </div>
        </div>

        <div className="system-prompt-section">
          <div className="section-header">
            <h3>SYSTEM <FaQuestionCircle className="help-icon" /></h3>
            <div className="token-count">0 <span className="token-icon">⚡</span></div>
          </div>
          
          <div className="system-prompt-options">
            <div className="jinja-toggle">
              Jinja <input type="checkbox" className="toggle-switch" />
            </div>
            <div className="variable-insert">{'{x}'}</div>
            <div className="expand-button"><FaExpand /></div>
          </div>
          
          <div className="system-prompt-input">
            <textarea 
              placeholder="ここにプロンプトワードを入力してください。変数を挿入..."
              className="prompt-textarea"
            />
          </div>
          
          <div className="add-message-button">
            <FaPlus /> メッセージ追加
          </div>
        </div>

        <div className="vision-section">
          <div className="section-header">
            <h3>ビジョン <FaQuestionCircle className="help-icon" /></h3>
            <div className="toggle-container">
              <input 
                type="checkbox" 
                className="toggle-switch" 
                checked={visionEnabled}
                onChange={() => setVisionEnabled(!visionEnabled)}
              />
            </div>
          </div>
        </div>

        <div className="output-section">
          <div className="section-header">
            <h3>出力変数 <span className="optional-mark">▸</span></h3>
            <div className="toggle-container">
              <div className="toggle-label">構造化出力 <FaQuestionCircle className="help-icon" /></div>
              <input 
                type="checkbox" 
                className="toggle-switch" 
                checked={structuredOutput}
                onChange={() => setStructuredOutput(!structuredOutput)}
              />
            </div>
          </div>
        </div>

        <div className="retry-section">
          <div className="section-header">
            <h3>失敗時再試行</h3>
            <div className="toggle-container">
              <input 
                type="checkbox" 
                className="toggle-switch" 
                checked={retryOnFailure}
                onChange={() => setRetryOnFailure(!retryOnFailure)}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LLMNodeDetailPanel;
