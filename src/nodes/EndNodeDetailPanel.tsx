import React, { useState, useRef } from 'react';
import { FaTimes, FaPlus, FaBook, FaEllipsisH, FaTrash, FaCode } from 'react-icons/fa';
import { FaStop } from 'react-icons/fa';
import { BiSearch } from 'react-icons/bi';
import './css/NodeDetailPanel.css';
import './css/EndNodeDetailPanel.css';

interface EndNodeDetailPanelProps {
  node: any;
  onClose: () => void;
}

const EndNodeDetailPanel: React.FC<EndNodeDetailPanelProps> = ({ node, onClose }) => {
  const [description, setDescription] = useState<string>('');
  const [showVariableDropdown, setShowVariableDropdown] = useState(false);
  const [searchText, setSearchText] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 输出变量
  const outputVariables = [
    { id: 'result', type: 'String' }
  ];

  // 可选变量列表
  const availableVariables = [
    { id: 'result', type: 'String', source: 'コード実行' },
    { id: 'text', type: 'String', source: '開始' },
    { id: 'sys.user_id', type: 'String', source: '' },
    { id: 'sys.files', type: 'Array[File]', source: '' },
    { id: 'sys.app_id', type: 'String', source: '' },
    { id: 'sys.workflow_id', type: 'String', source: '' },
    { id: 'sys.workflow_run_id', type: 'String', source: '' },
  ];

  const filteredVariables = availableVariables.filter(variable => 
    variable.id.toLowerCase().includes(searchText.toLowerCase()) ||
    variable.source.toLowerCase().includes(searchText.toLowerCase())
  );

  const handleClickOutside = (e: MouseEvent) => {
    if (dropdownRef.current && !dropdownRef.current.contains(e.target as Node)) {
      setShowVariableDropdown(false);
    }
  };

  React.useEffect(() => {
    if (showVariableDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showVariableDropdown]);

  return (
    <div className="node-detail-panel">
      <div className="node-detail-header">
        <div className="node-detail-title">
          <div className="node-icon end-node-icon-color">
            <FaStop />
          </div>
          <h2>{node.data.label || '終了'}</h2>
        </div>
        <div className="node-detail-actions">
          <button className="icon-button" onClick={onClose}>
            <FaTimes />
          </button>
        </div>
      </div>

      <div className="node-detail-content">
        <div className="description-section">
          <textarea
            placeholder="説明を追加..."
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="description-input"
          />
        </div>

        <div className="output-fields-section">
          <div className="section-header">
            <h3>出力変数</h3>
            <button className="add-field-button">
              <FaPlus />
            </button>
          </div>
          
          <div className="output-variables">
            {outputVariables.map((variable) => (
              <div key={variable.id} className="output-variable-item">
                <div className="output-variable-name">{variable.id}</div>
                <div className="output-variable-type">
                  <div className="code-execution-tag">
                    <FaCode /> コード実行
                  </div>
                  <div className="variable-type-tag">{variable.type}</div>
                </div>
                <button className="delete-variable-button">
                  <FaTrash />
                </button>
              </div>
            ))}
          </div>
          
          <div className="variable-dropdown-container" style={{ position: 'relative' }}>
            {showVariableDropdown && (
              <div ref={dropdownRef} className="variable-dropdown">
                <div className="variable-search">
                  <BiSearch className="search-icon" />
                  <input 
                    type="text" 
                    placeholder="変数を検索" 
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    className="variable-search-input"
                  />
                </div>
                <div className="variable-list">
                  {filteredVariables.map((variable) => (
                    <div key={variable.id} className="variable-list-item">
                      <div className="variable-list-item-left">
                        <span className="variable-tag-x">{'{x}'}</span>
                        <span className="variable-name">{variable.id}</span>
                      </div>
                      <div className="variable-list-item-right">
                        {variable.source && (
                          <div className="variable-source">{variable.source}</div>
                        )}
                        <div className="variable-type">{variable.type}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EndNodeDetailPanel;
