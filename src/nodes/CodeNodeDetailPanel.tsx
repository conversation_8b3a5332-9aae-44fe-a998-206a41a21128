import React, { useState } from 'react';
import { FaTimes, FaPlus, FaTrash, FaCode, FaExpand, FaCopy, FaChevronDown, FaChevronRight, FaCheck } from 'react-icons/fa';
import { useReactFlow } from '@xyflow/react';
import './css/NodeDetailPanel.css';
import './css/CodeNodeDetailPanel.css';

interface CodeNodeDetailPanelProps {
  node: any;
  onClose: () => void;
}

const CodeNodeDetailPanel: React.FC<CodeNodeDetailPanelProps> = ({ node, onClose }) => {
  const { setNodes } = useReactFlow();
  const [description, setDescription] = useState<string>('');
  const [inputVariables, setInputVariables] = useState<Array<{name: string, value: string}>>([
    { name: 'arg1', value: '' },
    { name: 'arg2', value: '' },
    { name: 'arg3', value: '' }
  ]);
  const [code, setCode] = useState<string>(`def main(arg1: str, arg2: str, arg3: str) -> dict:
    return {
        "result": arg1 + arg2 + arg3
    }`);
  const [outputExpanded, setOutputExpanded] = useState<boolean>(true);
  const [outputVariables, setOutputVariables] = useState<Array<{name: string, type: string}>>([
    { name: 'result', type: 'Object' }
  ]);
  const [retryOnFailure, setRetryOnFailure] = useState<boolean>(false);
  const [exceptionHandling, setExceptionHandling] = useState<boolean>(false);
  const [showTypeDropdown, setShowTypeDropdown] = useState<boolean>(false);
  const [selectedType, setSelectedType] = useState<string>('Object');
  const [activeDropdownIndex, setActiveDropdownIndex] = useState<number>(-1);

  // 更新节点数据
  const updateNodeData = (data: any) => {
    setNodes((nodes) =>
      nodes.map((n) => {
        if (n.id === node.id) {
          return {
            ...n,
            data: {
              ...n.data,
              ...data,
            },
          };
        }
        return n;
      })
    );
  };

  // 添加输入变量
  const handleAddInputVariable = () => {
    setInputVariables([...inputVariables, { name: `arg${inputVariables.length + 1}`, value: '' }]);
  };

  // 删除输入变量
  const handleRemoveInputVariable = (index: number) => {
    const newInputVariables = [...inputVariables];
    newInputVariables.splice(index, 1);
    setInputVariables(newInputVariables);
  };

  // 更新输入变量名称
  const handleInputVariableNameChange = (index: number, name: string) => {
    const newInputVariables = [...inputVariables];
    newInputVariables[index].name = name;
    setInputVariables(newInputVariables);
  };

  // 更新输入变量值
  const handleInputVariableValueChange = (index: number, value: string) => {
    const newInputVariables = [...inputVariables];
    newInputVariables[index].value = value;
    setInputVariables(newInputVariables);
  };

  // 添加输出变量
  const handleAddOutputVariable = () => {
    setOutputVariables([...outputVariables, { name: `output${outputVariables.length + 1}`, type: 'String' }]);
  };

  // 删除输出变量
  const handleRemoveOutputVariable = (index: number) => {
    const newOutputVariables = [...outputVariables];
    newOutputVariables.splice(index, 1);
    setOutputVariables(newOutputVariables);
  };

  // 更新输出变量名称
  const handleOutputVariableNameChange = (index: number, name: string) => {
    const newOutputVariables = [...outputVariables];
    newOutputVariables[index].name = name;
    setOutputVariables(newOutputVariables);
  };

  // 更新输出变量类型
  const handleOutputVariableTypeChange = (index: number, type: string) => {
    const newOutputVariables = [...outputVariables];
    newOutputVariables[index].type = type;
    setOutputVariables(newOutputVariables);
    setShowTypeDropdown(false);
    setActiveDropdownIndex(-1);
  };

  // 切换类型下拉菜单
  const toggleTypeDropdown = (index: number) => {
    if (activeDropdownIndex === index) {
      setActiveDropdownIndex(-1);
      setShowTypeDropdown(false);
    } else {
      setActiveDropdownIndex(index);
      setShowTypeDropdown(true);
    }
  };

  return (
    <div className="node-detail-panel">
      <div className="node-detail-header">
        <div className="node-detail-title">
          <div className="node-icon code-node-icon-color">
            <FaCode />
          </div>
          <h2>{node.data.label || 'コード実行'}</h2>
        </div>
        <div className="node-detail-actions">
          <button className="icon-button" onClick={onClose}>
            <FaTimes />
          </button>
        </div>
      </div>

      <div className="node-detail-content">
        <div className="description-section">
          <textarea
            placeholder="説明を追加..."
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="description-input"
          />
        </div>

        <div className="input-variables-section">
          <div className="section-header">
            <h3>入力変数</h3>
            <button className="add-variable-button" onClick={handleAddInputVariable}>
              <FaPlus />
            </button>
          </div>

          <div className="input-variables-list">
            {inputVariables.map((variable, index) => (
              <div key={index} className="input-variable-item">
                <div className="variable-name-field">
                  <input
                    type="text"
                    value={variable.name}
                    onChange={(e) => handleInputVariableNameChange(index, e.target.value)}
                  />
                </div>
                <div className="variable-value-field">
                  <div className="variable-placeholder">
                    <span className="variable-icon">{'{x}'}</span> 変数値を設定
                  </div>
                </div>
                <button className="remove-variable-button" onClick={() => handleRemoveInputVariable(index)}>
                  <FaTrash />
                </button>
              </div>
            ))}
          </div>
        </div>

        <div className="code-editor-section">
          <div className="code-editor-header">
            <div className="language-selector">
              <span className="language-label">PYTHON3</span>
              <FaChevronDown className="language-dropdown-icon" />
            </div>
            <div className="code-editor-actions">
              <button className="code-action-button">
                <FaPlus />
              </button>
              <button className="code-action-button">
                <FaCopy />
              </button>
              <button className="code-action-button">
                <FaExpand />
              </button>
            </div>
          </div>

          <div className="code-editor">
            <div className="line-numbers">
              <div>1</div>
              <div>2</div>
              <div>3</div>
              <div>4</div>
              <div>5</div>
              <div>6</div>
            </div>
            <textarea
              value={code}
              onChange={(e) => setCode(e.target.value)}
              className="code-textarea"
            />
            {showTypeDropdown && activeDropdownIndex === -1 && (
              <div className="type-dropdown">
                <div className="type-option">String</div>
                <div className="type-option">Number</div>
                <div className="type-option">Array[Number]</div>
                <div className="type-option">Array[String]</div>
                <div className="type-option">Array[Object]</div>
                <div className="type-option selected">
                  Object <FaCheck className="selected-icon" />
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="output-variables-section">
          <div className="section-header" onClick={() => setOutputExpanded(!outputExpanded)}>
            <h3>出力変数 <span className="required-mark">*</span></h3>
            <div className="expand-icon">
              {outputExpanded ? <FaChevronDown /> : <FaChevronRight />}
            </div>
          </div>

          {outputExpanded && (
            <div className="output-variables-list">
              {outputVariables.map((variable, index) => (
                <div key={index} className="output-variable-item">
                  <div className="variable-name-field">
                    <input
                      type="text"
                      value={variable.name}
                      onChange={(e) => handleOutputVariableNameChange(index, e.target.value)}
                    />
                  </div>
                  <div className="variable-type-field" onClick={() => toggleTypeDropdown(index)}>
                    <span>{variable.type}</span>
                    <FaChevronDown className="type-dropdown-icon" />

                    {showTypeDropdown && activeDropdownIndex === index && (
                      <div className="type-dropdown">
                        <div className="type-option" onClick={() => handleOutputVariableTypeChange(index, 'String')}>
                          String {variable.type === 'String' && <FaCheck className="selected-icon" />}
                        </div>
                        <div className="type-option" onClick={() => handleOutputVariableTypeChange(index, 'Number')}>
                          Number {variable.type === 'Number' && <FaCheck className="selected-icon" />}
                        </div>
                        <div className="type-option" onClick={() => handleOutputVariableTypeChange(index, 'Array[Number]')}>
                          Array[Number] {variable.type === 'Array[Number]' && <FaCheck className="selected-icon" />}
                        </div>
                        <div className="type-option" onClick={() => handleOutputVariableTypeChange(index, 'Array[String]')}>
                          Array[String] {variable.type === 'Array[String]' && <FaCheck className="selected-icon" />}
                        </div>
                        <div className="type-option" onClick={() => handleOutputVariableTypeChange(index, 'Array[Object]')}>
                          Array[Object] {variable.type === 'Array[Object]' && <FaCheck className="selected-icon" />}
                        </div>
                        <div className="type-option" onClick={() => handleOutputVariableTypeChange(index, 'Object')}>
                          Object {variable.type === 'Object' && <FaCheck className="selected-icon" />}
                        </div>
                      </div>
                    )}
                  </div>
                  <button className="remove-variable-button" onClick={() => handleRemoveOutputVariable(index)}>
                    <FaTrash />
                  </button>
                </div>
              ))}

              <button className="add-output-variable-button" onClick={handleAddOutputVariable}>
                <FaPlus />
              </button>
            </div>
          )}
        </div>

        <div className="retry-section">
          <div className="section-header">
            <h3>失敗時再試行</h3>
            <label className="toggle-switch">
              <input
                type="checkbox"
                checked={retryOnFailure}
                onChange={() => setRetryOnFailure(!retryOnFailure)}
              />
              <span className="toggle-slider"></span>
            </label>
          </div>
        </div>

        <div className="exception-section">
          <div className="section-header">
            <h3>例外処理 <FaChevronRight className="help-icon" /></h3>
            <div className="exception-dropdown">
              <span>デフォルト値</span>
              <FaChevronDown className="dropdown-icon" />
            </div>
          </div>

          <div className="exception-description">
            例外発生時のデフォルト出力
          </div>

          <div className="exception-result">
            <div className="result-header">
              <span>result <span className="result-type">object</span></span>
              <div className="result-actions">
                <button className="result-action-button">
                  <FaCopy />
                </button>
                <button className="result-action-button">
                  <FaExpand />
                </button>
              </div>
            </div>

            <div className="result-code">
              <div className="line-numbers">
                <div>1</div>
                <div>2</div>
                <div>3</div>
                <div>4</div>
              </div>
              <pre className="result-content">
{`{
    "success": false,
    "message": "python code error."
}`}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CodeNodeDetailPanel;
