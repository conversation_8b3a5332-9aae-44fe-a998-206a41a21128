import { memo } from 'react';
import { Position, Handle, useReactFlow, type NodeProps, type Node } from '@xyflow/react';
import { FaStop } from 'react-icons/fa';
import './css/EndNode.css';

// 导入全局事件总线
import { useNodeEvents } from '../utils/NodeEventContext';

function EndNode({ id, data }: NodeProps<Node<{ label?: string }>>) {
  const { updateNodeData } = useReactFlow();
  const { selectNode } = useNodeEvents();

  const handleNodeClick = () => {
    // 触发节点选择事件，传递当前节点信息
    selectNode({ id, data, type: 'endNode' });
  };

  return (
    <div className="end-node" onClick={handleNodeClick}>
      <div className="end-node-header">
        <div className="end-node-icon">
          <FaStop />
        </div>
        <div className="end-node-title">
          {data.label || '終了'}
        </div>
      </div>

      <Handle
        type="target"
        position={Position.Left}
        style={{
          background: '#e74c3c',
          width: '8px',
          height: '8px',
          top: '50%',
          border: '2px solid white'
        }}
      />
    </div>
  );
}

export default memo(EndNode);
