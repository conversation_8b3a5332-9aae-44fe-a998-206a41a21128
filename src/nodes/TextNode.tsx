import { memo } from 'react';
import { Posi<PERSON>, Handle, useReactFlow, type NodeProps, type Node } from '@xyflow/react';
import './css/TextNode.css';

function TextNode({ id, data }: NodeProps<Node<{ text: string }>>) {
  const { updateNodeData } = useReactFlow();

  return (
    <div style={{ border: '1px solid #ccc', padding: '10px', borderRadius: '8px', backgroundColor: '#fff' }}>
      <div>node {id}</div>
      <div>
        <input
          onChange={(evt) => updateNodeData(id, { text: evt.target.value })}
          value={data.text}
          className="xy-theme__input"
        />
      </div>
      <Handle type="source" position={Position.Right}
      style={{ background: 'red' }}
      />
    </div>
  );
}

export default memo(TextNode);