.end-node-icon-color {
    background-color: #fde7e7 !important;
    color: #e74c3c !important;
}

.output-fields-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.output-variables {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.output-variable-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f9f9f9;
    border-radius: 8px;
    font-size: 14px;
}

.output-variable-name {
    font-weight: 500;
    color: #333;
}

.output-variable-type {
    display: flex;
    align-items: center;
    gap: 8px;
}

.code-execution-tag {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    font-size: 12px;
    color: #666;
}

.variable-type-tag {
    color: #4285f4;
    font-weight: 500;
}

.delete-variable-button {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 14px;
    padding: 4px;
}

.delete-variable-button:hover {
    color: #e74c3c;
}

.variable-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1100;
    max-height: 400px;
    overflow-y: auto;
}

.variable-search {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
}

.search-icon {
    color: #999;
    margin-right: 8px;
    font-size: 14px;
}

.variable-search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 14px;
    color: #333;
    background: transparent;
}

.variable-list {
    padding: 6px 0;
}

.variable-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.variable-list-item:hover {
    background-color: #f5f5f5;
}

.variable-list-item-left {
    display: flex;
    align-items: center;
    gap: 6px;
}

.variable-tag-x {
    color: #4285f4;
    font-weight: 500;
}

.variable-name {
    color: #333;
}

.variable-list-item-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.variable-source {
    padding: 2px 6px;
    background-color: #f0f0f0;
    border-radius: 4px;
    font-size: 12px;
    color: #666;
}

.variable-dropdown-container {
    position: relative;
    width: 100%;
}