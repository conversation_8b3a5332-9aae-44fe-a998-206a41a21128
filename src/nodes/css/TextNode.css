:root {
    --color-primary: #ff0073;
    --color-background: #fefefe;
    --color-hover-bg: #f6f6f6;
    --color-disabled: #76797e;
  }
  
.xy-theme__input {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--color-primary);
  border-radius: 7px;
  background-color: var(--color-background);
  transition:
    background-color 0.2s ease,
    border-color 0.2s ease;
  font-size: 1rem;
  color: inherit;
}

.xy-theme__input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(255, 0, 115, 0.3);
}
