.node-detail-panel {
    position: fixed;
    top: 150px;
    /* 留出顶部空间，不遮挡save按钮 */
    right: 10px;
    width: 400px;
    height: calc(100vh - 60px);
    /* 减去顶部空间的高度 */
    background-color: white;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    border-radius: 8px 0 0 0;
    /* 左上角圆角 */
    /* スクロール時に最下部が見切れないように余白をつける */
    padding-bottom: 80px;
}

.node-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
}

.node-detail-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.node-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background-color: #4285f4;
    border-radius: 8px;
    color: white;
    font-size: 12px;
}

.node-detail-title h2 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

.node-detail-actions {
    display: flex;
    gap: 12px;
}

.icon-button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    color: #666;
    padding: 6px;
    border-radius: 4px;
}

.icon-button:hover {
    background-color: #f5f5f5;
}

.node-detail-content {
    padding: 12px;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.description-section {
    border-bottom: 1px solid #eee;
}

.description-input {
    width: 100%;
    min-height: 40px;
    border: none;
    resize: none;
    font-size: 14px;
    color: #666;
    outline: none;
    padding: 4px 0;
}

.description-input::placeholder {
    color: #aaa;
}

.input-fields-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
}

.add-field-button {
    background: none;
    border: none;
    cursor: pointer;
    color: #4285f4;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
}

.add-field-button:hover {
    background-color: #f0f7ff;
}

.section-info {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.system-variables {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.variable-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f9f9f9;
    border-radius: 8px;
    font-size: 14px;
}

.variable-name {
    display: flex;
    align-items: center;
    gap: 6px;
}

.variable-tag {
    color: #4285f4;
    font-weight: 500;
}

.legacy-tag {
    background-color: #f0f0f0;
    color: #666;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 4px;
    margin-left: 8px;
}

.variable-type {
    color: #666;
}


/* 环境变量弹出框样式 */

.env-variable-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.env-variable-content {
    background-color: white;
    border-radius: 12px;
    width: 500px;
    max-width: 90%;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.env-variable-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.env-variable-header h2 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.env-variable-close {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    color: #666;
}

.env-variable-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.env-variable-field {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.env-variable-field label {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.env-variable-types {
    display: flex;
    gap: 8px;
    margin-top: 5px;
}

.type-option {
    flex: 1;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.type-option.selected {
    border-color: #4285f4;
    background-color: #f0f7ff;
    color: #4285f4;
    font-weight: 500;
}

.env-variable-input {
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    background-color: #f5f5f5;
}

.env-variable-input::placeholder {
    color: #aaa;
}

.env-variable-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 20px;
}

.env-variable-button {
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.env-variable-cancel {
    background-color: white;
    border: 1px solid #ddd;
    color: #333;
}

.env-variable-save {
    background-color: #4285f4;
    border: none;
    color: white;
}