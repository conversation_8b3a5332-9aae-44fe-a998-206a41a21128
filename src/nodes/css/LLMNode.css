.llm-node {
  border: 1px solid #e0e0e0;
  padding: 8px;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  width: 200px;
  transition: box-shadow 0.3s ease;
}

.llm-node:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  background-color: #f4f6f6;
}

.llm-node-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.llm-node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background-color: #5468ff;
  border-radius: 6px;
  margin-right: 6px;
}

.llm-node-icon svg {
  width: 16px;
  height: 16px;
  color: white;
}

.llm-node-title {
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

.llm-node-model {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  background-color: #f5f5f5;
  border-radius: 6px;
  font-size: 13px;
  color: #333;
}

.llm-model-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background-color: #10a37f;
  border-radius: 4px;
  margin-right: 6px;
}

.llm-model-icon img {
  width: 14px;
  height: 14px;
  color: white;
}

.llm-model-name {
  flex: 1;
  font-weight: 500;
}

.llm-model-tag {
  font-size: 11px;
  padding: 2px 6px;
  background-color: #e0e0e0;
  border-radius: 4px;
  color: #666;
  margin-left: 6px;
}
