.dataset-grep-node-icon-color {
    background-color: #10b981;
}

.search-variable-section,
.knowledge-base-section,
.metadata-filter-section,
.output-section {
    margin-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    cursor: pointer;
}

.section-header h3 {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin: 0;
}

.required-mark {
    color: #f56565;
}

.optional-mark {
    color: #a0aec0;
}

.help-icon {
    font-size: 14px;
    color: #a0aec0;
    margin-left: 5px;
    cursor: pointer;
}

.variable-selector {
    background-color: #f5f7f9;
    border-radius: 6px;
    padding: 12px;
    cursor: pointer;
}

.variable-placeholder {
    color: #718096;
    display: flex;
    align-items: center;
}

.variable-icon {
    margin-right: 8px;
    color: #4a6cf7;
}

.knowledge-actions {
    display: flex;
    align-items: center;
}

.search-settings-button {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    color: #4a6cf7;
    font-size: 13px;
    cursor: pointer;
    margin-right: 10px;
}

.search-settings-button svg {
    margin-right: 4px;
}

.add-knowledge-button {
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: #4a6cf7;
    font-size: 16px;
    cursor: pointer;
    width: 24px;
    height: 24px;
}

.selected-knowledge {
    display: flex;
    align-items: center;
    background-color: #f5f7f9;
    border-radius: 6px;
    padding: 10px;
    margin-top: 10px;
}

.knowledge-icon {
    color: #4a6cf7;
    margin-right: 8px;
}

.knowledge-name {
    flex: 1;
    font-size: 13px;
}

.knowledge-actions {
    display: flex;
}

.edit-button,
.delete-button {
    background: none;
    border: none;
    color: #718096;
    cursor: pointer;
    padding: 4px;
}

.filter-dropdown {
    position: relative;
}

.filter-select {
    appearance: none;
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 6px 12px;
    padding-right: 30px;
    font-size: 13px;
    cursor: pointer;
}

.next-step-section {
    margin-top: 20px;
}

.next-step-section h3 {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin: 0 0 5px 0;
}

.next-step-section p {
    font-size: 13px;
    color: #718096;
    margin: 0 0 15px 0;
}

.next-node-selector {
    display: flex;
    align-items: center;
    background-color: #f5f7f9;
    border-radius: 8px;
    padding: 15px;
    border: 1px dashed #cbd5e0;
}

.next-node-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background-color: #10b981;
    border-radius: 6px;
    color: white;
}

.next-node-line {
    flex: 1;
    height: 2px;
    background-color: #e2e8f0;
    margin: 0 10px;
}

.add-next-node-button {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    color: #718096;
    font-size: 13px;
    cursor: pointer;
}

.add-next-node-button svg {
    margin-right: 5px;
}


/* 知识选择模态框样式 */

.knowledge-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.knowledge-modal {
    background-color: white;
    border-radius: 12px;
    width: 500px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.knowledge-modal h2 {
    font-size: 18px;
    font-weight: 500;
    margin: 0 0 20px 0;
    text-align: center;
}

.knowledge-item {
    display: flex;
    align-items: center;
    background-color: #eef3ff;
    border: 1px solid #4a6cf7;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.knowledge-item-icon {
    color: #4a6cf7;
    margin-right: 10px;
}

.knowledge-item-name {
    flex: 1;
    font-size: 14px;
}

.knowledge-item-tag {
    background-color: #f5f7f9;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    color: #4a5568;
}

.knowledge-count {
    font-size: 14px;
    color: #4a5568;
    margin-bottom: 20px;
}

.knowledge-modal-actions {
    display: flex;
    justify-content: flex-end;
}

.cancel-button {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    margin-right: 10px;
}

.add-button {
    background-color: #4a6cf7;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
}


/* 输出变量样式 */

.output-variables {
    margin-top: 15px;
}

.output-variable {
    background-color: #f8fafc;
    border-radius: 6px;
    padding: 15px;
}

.variable-name {
    font-size: 14px;
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 4px;
}

.variable-type {
    font-size: 13px;
    color: #718096;
    font-weight: normal;
    margin-left: 5px;
}

.variable-description {
    font-size: 13px;
    color: #718096;
    margin-bottom: 15px;
}

.variable-properties {
    margin-left: 15px;
    border-left: 1px solid #e2e8f0;
    padding-left: 15px;
}

.property {
    margin-bottom: 12px;
}

.property:last-child {
    margin-bottom: 0;
}

.property-name {
    font-size: 13px;
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 2px;
}

.property-type {
    font-size: 12px;
    color: #718096;
    font-weight: normal;
    margin-left: 5px;
}

.property-description {
    font-size: 12px;
    color: #718096;
}