import { memo } from 'react';
import { Position, Handle, useReactFlow, type NodeProps, type Node } from '@xyflow/react';
import { FaRobot } from 'react-icons/fa';
import './css/LLMNode.css';

// 导入全局事件总线
import { useNodeEvents } from '../utils/NodeEventContext';

interface LLMNodeData {
  label?: string;
  model?: string;
}

function LLMNode({ id, data }: NodeProps<Node<LLMNodeData>>) {
  const { updateNodeData } = useReactFlow();
  const { selectNode } = useNodeEvents();

  const handleNodeClick = () => {
    // 触发节点选择事件，传递当前节点信息
    selectNode({ id, data, type: 'llmNode' });
  };

  return (
    <div className="llm-node" onClick={handleNodeClick}>
      <div className="llm-node-header">
        <div className="llm-node-icon">
          <FaRobot />
        </div>
        <div className="llm-node-title">
          {data.label || 'LLM'}
        </div>
      </div>
      
      {data.model && (
        <div className="llm-node-model">
          <div className="llm-model-icon">
            <img src="/openai-logo.svg" alt="Model" width="16" height="16" />
          </div>
          <div className="llm-model-name">
            {data.model}
          </div>
          <div className="llm-model-tag">
            CHAT
          </div>
        </div>
      )}
      
      <Handle
        type="target"
        position={Position.Left}
        style={{
          background: '#4a90e2',
          width: '8px',
          height: '8px',
          top: '50%',
          border: '1px solid white'
        }}
      />
      
      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: '#4a90e2',
          width: '8px',
          height: '8px',
          top: '50%',
          border: '1px solid white'
        }}
      />
    </div>
  );
}

export default memo(LLMNode);
