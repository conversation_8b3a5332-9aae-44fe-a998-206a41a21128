import { memo } from 'react';
import { Position, Handle, useReactFlow, type NodeProps, type Node } from '@xyflow/react';
import { FaGlobe } from 'react-icons/fa';
import './css/HttpRequestNode.css';

// 导入全局事件总线
import { useNodeEvents } from '../utils/NodeEventContext';

interface HttpRequestNodeData {
  label?: string;
  method?: string;
  url?: string;
}

function HttpRequestNode({ id, data }: NodeProps<Node<HttpRequestNodeData>>) {
  console.log('Rendering HttpRequestNode with id:', id, 'and data:', data);
  const { updateNodeData } = useReactFlow();
  const { selectNode } = useNodeEvents();

  const handleNodeClick = (e: React.MouseEvent) => {
    // 阻止事件冒泡，防止触发ReactFlow的选择事件
    e.stopPropagation();

    // 触发节点选择事件，传递当前节点信息
    const nodeInfo = { id, data, type: 'httpRequestNode' };
    console.log('HTTP Request Node clicked', nodeInfo);
    selectNode(nodeInfo);
  };

  return (
    <div className="http-request-node" onClick={handleNodeClick}>
      <div className="http-request-node-header">
        <div className="http-request-node-icon">
          <FaGlobe />
        </div>
        <div className="http-request-node-title">
          {data.label || 'HTTPリクエスト'}
        </div>
      </div>

      <Handle
        type="target"
        position={Position.Left}
        style={{
          background: '#4a90e2',
          width: '8px',
          height: '8px',
          top: '50%',
          border: '1px solid white'
        }}
      />

      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: '#4a90e2',
          width: '8px',
          height: '8px',
          top: '50%',
          border: '1px solid white'
        }}
      />
    </div>
  );
}

export default memo(HttpRequestNode);
