import { memo } from 'react';
import { Position, Handle, useReactFlow, type NodeProps, type Node } from '@xyflow/react';
import { IoBookOutline } from 'react-icons/io5';
import { FaFolder } from 'react-icons/fa';
import './css/DatasetGrepNode.css';

// 导入全局事件总线
import { useNodeEvents } from '../utils/NodeEventContext';

interface DatasetGrepNodeData {
  label?: string;
  dataset?: string;
}

function DatasetGrepNode({ id, data }: NodeProps<Node<DatasetGrepNodeData>>) {
  const { updateNodeData } = useReactFlow();
  const { selectNode } = useNodeEvents();

  const handleNodeClick = () => {
    // 触发节点选择事件，传递当前节点信息
    selectNode({ id, data, type: 'datasetGrepNode' });
  };

  return (
    <div className="dataset-grep-node" onClick={handleNodeClick}>
      <div className="dataset-grep-node-header">
        <div className="dataset-grep-node-icon">
          <IoBookOutline />
        </div>
        <div className="dataset-grep-node-title">
          {data.label || '知識検索'}
        </div>
      </div>
      
      {data.dataset && (
        <div className="dataset-grep-node-dataset">
          <div className="dataset-icon">
            <FaFolder />
          </div>
          <div className="dataset-name">
            {data.dataset}
          </div>
        </div>
      )}
      
      <Handle
        type="target"
        position={Position.Left}
        style={{
          background: '#4a90e2',
          width: '8px',
          height: '8px',
          top: '50%',
          border: '1px solid white'
        }}
      />
      
      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: '#4a90e2',
          width: '8px',
          height: '8px',
          top: '50%',
          border: '1px solid white'
        }}
      />
    </div>
  );
}

export default memo(DatasetGrepNode);
