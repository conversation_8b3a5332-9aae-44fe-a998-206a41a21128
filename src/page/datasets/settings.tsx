import React from "react";
import { useNavigate } from "react-router-dom";
import "./settings.css";
import { FaArrowLeft, FaRobot, FaFileAlt, FaExternalLinkAlt, FaCheck } from "react-icons/fa";
import { MdOutlineAutoAwesome } from "react-icons/md";
import { IoDocumentTextOutline } from "react-icons/io5";

const DatasetSettings = () => {
  const navigate = useNavigate();
  const datasetName = "社内業務まとめ資料.pdf...";

  return (
    <div className="datasetSettingsRoot">
      <div className="datasetSettingsContainer">
      {/* 顶部导航和步骤指示器 */}
      <div className="datasetCreateTopBar">
        <button className="datasetCreateBackButton" onClick={() => navigate("/datasets")}>
          <FaArrowLeft />
          <span>ナレッジベース</span>
        </button>

        <div className="datasetCreateSteps">
          <div className="datasetCreateStepItem">
            <div className="datasetCreateStepBadge active">Step1</div>
            <div className="datasetCreateStepText active">データソース</div>
          </div>
          <div className="datasetCreateStepLine"></div>
          <div className="datasetCreateStepItem">
            <div className="datasetCreateStepBadge inactive">Step2</div>
            <div className="datasetCreateStepText">テキスト抽出</div>
          </div>
          <div className="datasetCreateStepLine"></div>
          <div className="datasetCreateStepItem">
            <div className="datasetCreateStepBadge inactive">Step3</div>
            <div className="datasetCreateStepText">索引と設定</div>
          </div>
        </div>
      </div>

        <div className="datasetSettingsContent">
          <div className="datasetSettingsSuccessSection">
            <h2 className="datasetSettingsSuccessTitle">
              🎉 ナレッジベースが作成されました
            </h2>
            <p className="datasetSettingsSuccessDescription">
              ナレッジベースの名前は自動的に設定されましたが、自由に変更できます。
            </p>
          </div>

          <div className="datasetSettingsNameSection">
            <div className="datasetSettingsNameIcon">
              <FaRobot />
            </div>
            <div className="datasetSettingsNameInputContainer">
              <div className="datasetSettingsNameLabel">ナレッジベース名</div>
              <div className="datasetSettingsNameDisplay">
                {datasetName}
              </div>
            </div>
          </div>

          <div className="datasetSettingsCompletedSection">
            <h3 className="datasetSettingsCompletedTitle">埋め込みが完了しました</h3>

            <div className="datasetSettingsFileItem">
              <div className="datasetSettingsFileItemIcon pdf">
                <FaFileAlt />
              </div>
              <div className="datasetSettingsFileItemName">
              社内業務まとめ資料.pdf
              </div>
              <div className="datasetSettingsFileItemStatus">
                <FaCheck className="datasetSettingsFileItemStatusIcon" />
              </div>
            </div>
          </div>

          <div className="datasetSettingsInfoSection">
            <div className="datasetSettingsInfoItem">
              <div className="datasetSettingsInfoRow">
                <div className="datasetSettingsInfoColumn">
                  <div className="datasetSettingsInfoLabel">チャンキングモード</div>
                  <div className="datasetSettingsInfoValue">カスタム</div>
                </div>
                <div className="datasetSettingsInfoColumn">
                  <div className="datasetSettingsInfoLabel">最大なチャンクの長さ</div>
                  <div className="datasetSettingsInfoValue">1024</div>
                </div>
              </div>
            </div>
            <div className="datasetSettingsInfoItem">
              <div className="datasetSettingsInfoLabel">テキストの前処理ルール</div>
              <div className="datasetSettingsInfoValue">
                <div className="datasetSettingsInfoTag blue">
                  連続するスペース、改行、タブを置換する
                </div>
              </div>
            </div>
            <div className="datasetSettingsInfoItem">
              <div className="datasetSettingsInfoRow">
                <div className="datasetSettingsInfoColumn">
                  <div className="datasetSettingsInfoLabel">インデックス方法</div>
                  <div className="datasetSettingsInfoValue">
                    <div className="datasetSettingsInfoTag orange">
                      <MdOutlineAutoAwesome className="datasetSettingsInfoTagIcon" />
                      高品質
                    </div>
                  </div>
                </div>
                <div className="datasetSettingsInfoColumn">
                  <div className="datasetSettingsInfoLabel">検索設定</div>
                  <div className="datasetSettingsInfoValue">
                    <div className="datasetSettingsInfoTag purple">
                      <IoDocumentTextOutline className="datasetSettingsInfoTagIcon" />
                      ハイブリッド検索
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="datasetSettingsApiSection">
            <button className="datasetSettingsApiButton">
              <FaExternalLinkAlt className="datasetSettingsApiIcon" />
              Access the API
            </button>
            <button className="datasetSettingsDocButton">
              <FaExternalLinkAlt className="datasetSettingsApiIcon" />
              ドキュメントに移動
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DatasetSettings;
