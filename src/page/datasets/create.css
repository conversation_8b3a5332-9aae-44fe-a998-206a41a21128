.datasetCreateRoot {
    width: 100%;
    height: 100%;
    background: #f5f6fa;
    overflow: auto;
}

.datasetCreateContainer {
    width: 100%;
    margin: 0 auto;
    padding: 20px;
}


/* 顶部导航和步骤指示器 */

.datasetCreateTopBar {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 20px;
    width: 100%;
    padding: 10px 20px;
    background-color: #f5f6fa;
}

.datasetCreateBackButton {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    color: #333;
    font-size: 14px;
    cursor: pointer;
    padding: 0;
    margin-left: 0;
}

.datasetCreateBackButton:hover {
    color: #4a90e2;
}


/* 步骤指示器 */

.datasetCreateSteps {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    position: absolute;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 50%;
}

.datasetCreateStepItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    margin: 0 5px;
}

.datasetCreateStepBadge {
    width: auto;
    height: 24px;
    padding: 0 10px;
    border-radius: 12px;
    background-color: #e0e0e0;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 4px;
    font-size: 11px;
}

.datasetCreateStepBadge.active {
    background-color: #4a90e2;
    color: white;
}

.datasetCreateStepBadge.inactive {
    background-color: #e0e0e0;
    color: #666;
}

.datasetCreateStepText {
    font-size: 11px;
    color: #666;
}

.datasetCreateStepText.active {
    color: #4a90e2;
    font-weight: bold;
}

.datasetCreateStepLine {
    flex: 1;
    height: 1px;
    background-color: #e0e0e0;
    margin: 0 5px;
    margin-bottom: 4px;
    max-width: 60px;
}


/* 主要内容 */

.datasetCreateContent {
    background: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    width: 50%;
    margin: 0 auto;
}

.datasetCreateTitle {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
    text-align: left;
}


/* 数据源选项 */

.datasetCreateOptions {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.datasetCreateOption {
    width: 100%;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.2s;
}

.datasetCreateOption:hover {
    border-color: #4a90e2;
    background-color: #f5f9ff;
}

.datasetCreateOptionIcon {
    font-size: 20px;
    color: #4a90e2;
}

.datasetCreateOptionText {
    font-size: 14px;
    color: #333;
}


/* 文件上传区域 */

.datasetCreateUploadSection {
    margin-top: 30px;
}

.datasetCreateSubtitle {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
    text-align: left;
}

.datasetCreateDropzone {
    border: 2px dashed #e0e0e0;
    border-radius: 8px;
    padding: 30px;
    text-align: left;
    transition: all 0.2s;
    background-color: #f9f9f9;
    position: relative;
}

.datasetCreateDropzone.dragging {
    border-color: #4a90e2;
    background-color: #f5f9ff;
}

.datasetCreateDropzoneContent {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
}

.datasetCreateDropzoneIcon {
    font-size: 24px;
    color: #999;
}

.datasetCreateDropzoneText {
    font-size: 14px;
    color: #666;
    margin: 0;
    text-align: left;
}

.datasetCreateDropzoneHighlight {
    color: #4a90e2;
    font-weight: bold;
}

.datasetCreateDropzoneFormats {
    font-size: 12px;
    color: #999;
    max-width: 600px;
    margin: 0;
    text-align: left;
}

.datasetCreateFileInput {
    display: none;
}

.datasetCreateBrowseButton {
    display: inline-block;
    background-color: #4a90e2;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    margin-top: 10px;
    align-self: flex-start;
}

.datasetCreateBrowseButton:hover {
    background-color: #3a80d2;
}


/* 底部按钮 */

.datasetCreateActions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
}

.datasetCreateCheckboxContainer {
    display: flex;
    align-items: center;
    gap: 8px;
}

.datasetCreateCheckbox {
    width: 16px;
    height: 16px;
}

.datasetCreateCheckboxLabel {
    font-size: 14px;
    color: #333;
}

.datasetCreateNextButton {
    background-color: #4a90e2;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
}

.datasetCreateNextButton:hover {
    background-color: #3a80d2;
}