.datasetRoot {
    width: 100%;
    height: 100%;
    background: #f5f6fa;
    overflow: auto;
}

.datasetContainer {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}


/* 标签页样式 */

.datasetTabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 20px;
}

.datasetTabButton {
    padding: 10px 20px;
    background: transparent;
    border: none;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    position: relative;
}

.datasetTabButtonActive {
    color: #4a90e2;
    font-weight: 600;
}

.datasetTabButtonActive::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #4a90e2;
}


/* 过滤和搜索区域 */

.datasetFilterBar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.datasetFilterOptions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.datasetCheckboxLabel {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333;
    cursor: pointer;
}

.datasetCheckbox {
    margin-right: 8px;
}

.datasetTagSelector {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background: white;
    font-size: 14px;
    color: #333;
    cursor: pointer;
}

.datasetDropdownIcon {
    font-size: 10px;
    color: #999;
}

.datasetSearchAndApi {
    display: flex;
    align-items: center;
    gap: 15px;
}

.datasetSearchBar {
    position: relative;
}

.datasetSearchIcon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 14px;
}

.datasetSearchInput {
    padding: 8px 10px 8px 35px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    width: 200px;
}

.datasetApiButton {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
}

.datasetApiIcon {
    font-size: 12px;
    color: #666;
}


/* 主要内容区域 */

.datasetContent {
    display: flex;
    gap: 24px;
}


/* 左侧创建区域 */

.datasetCreateSection {
    width: 260px;
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.datasetCreateButton {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    cursor: pointer;
    height: 36px;
}

.datasetCreateIcon {
    font-size: 14px;
    color: #666;
}

.datasetCreateDescription {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 4px;
}

.datasetExternalButton {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 0;
    background: transparent;
    border: none;
    font-size: 12px;
    color: #4a90e2;
    cursor: pointer;
    text-align: left;
}

.datasetExternalIcon {
    font-size: 12px;
}


/* 右侧数据集列表 */

.datasetList {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.datasetCard {
    width: 260px;
    height: 160px;
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    display: flex;
    flex-direction: column;
}

.datasetCardHeader {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.datasetCardIcon {
    font-size: 22px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #f0f4ff;
    border-radius: 8px;
    color: #4a6cf7;
}

.datasetCardTitle {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.datasetCardDescription {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 16px;
}

.datasetCardFooter {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
}

.datasetCardTag {
    display: flex;
    align-items: center;
    color: #888;
    font-size: 12px;
}

.datasetCardTagIcon {
    margin-right: 4px;
    font-size: 14px;
}

.datasetCardActions {
    color: #888;
    font-size: 16px;
    cursor: pointer;
}
