import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import "./css/extract.css";
import { FaArrowLeft, FaSearch, FaFileAlt, FaExclamationTriangle, FaChevronDown, FaChevronUp, FaCheck } from "react-icons/fa";
import { FiFileText, FiSliders } from "react-icons/fi";
import { BsQuestionCircle, BsGrid3X3, BsTextLeft } from "react-icons/bs";
import { IoMdSwitch } from "react-icons/io";
import { RiRobot2Fill } from "react-icons/ri";

// 提示组件
const InfoTooltip = ({ content }: { content: string }) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <span className="tooltipContainer">
      <BsQuestionCircle
        className="infoIcon"
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      />
      {showTooltip && (
        <div className="tooltip">
          {content}
        </div>
      )}
    </span>
  );
};

// 推荐标签组件
const RecommendBadge = () => {
  return <span className="recommendBadge">推奨</span>;
};

const DatasetExtract = () => {
  const navigate = useNavigate();

  // 基本设置
  const [chunkSize, setChunkSize] = useState("1024");
  const [overlap, setOverlap] = useState("50");
  const [chunkIdentifier, setChunkIdentifier] = useState("\\n\\n");
  const [removeLineBreaks, setRemoveLineBreaks] = useState(true);
  const [removeUrls, setRemoveUrls] = useState(false);

  // 索引方法
  const [indexMethod, setIndexMethod] = useState("highQuality"); // highQuality, economic

  // 嵌入模型
  const [embeddingModel, setEmbeddingModel] = useState("text-embedding-3-large");
  const [showModelDropdown, setShowModelDropdown] = useState(false);

  // 搜索设置
  const [useRerank, setUseRerank] = useState(false);
  const [topK, setTopK] = useState(3);
  const [scoreThreshold, setScoreThreshold] = useState(0.5);

  // 处理返回上一步
  const handleBack = () => {
    navigate("/datasets/create");
  };

  // 处理下一步
  const handleNext = () => {
    navigate("/datasets/settings");
  };

  // 处理预览
  const handlePreview = () => {
    // 预览逻辑
    console.log("预览分块");
  };

  // 处理重置
  const handleReset = () => {
    setChunkIdentifier("\\n\\n");
    setChunkSize("1024");
    setOverlap("50");
    setRemoveLineBreaks(true);
    setRemoveUrls(false);
  };

  // 更新滑动条的活动部分颜色
  React.useEffect(() => {
    const topKSlider = document.querySelector('input[type="range"][min="1"][max="20"]') as HTMLInputElement;
    if (topKSlider) {
      const percent = ((topK - 1) / 19) * 100;
      topKSlider.style.setProperty('--value', `${percent}%`);
    }

    const scoreSlider = document.querySelector('input[type="range"][min="0"][max="1"]') as HTMLInputElement;
    if (scoreSlider) {
      const percent = (scoreThreshold / 1) * 100;
      scoreSlider.style.setProperty('--value', `${percent}%`);
    }
  }, [topK, scoreThreshold]);

  return (
    <div className="datasetExtractRoot">
      {/* 顶部导航和步骤指示器 */}
      <div className="datasetCreateTopBar">
        <button className="datasetCreateBackButton" onClick={handleBack}>
          <FaArrowLeft />
          <span>ナレッジベース</span>
        </button>

        <div className="datasetCreateSteps">
          <div className="datasetCreateStepItem">
            <div className="datasetCreateStepBadge active">Step1</div>
            <div className="datasetCreateStepText active">データソース</div>
          </div>
          <div className="datasetCreateStepLine"></div>
          <div className="datasetCreateStepItem">
            <div className="datasetCreateStepBadge inactive">Step2</div>
            <div className="datasetCreateStepText">テキスト抽出</div>
          </div>
          <div className="datasetCreateStepLine"></div>
          <div className="datasetCreateStepItem">
            <div className="datasetCreateStepBadge inactive">Step3</div>
            <div className="datasetCreateStepText">索引と設定</div>
          </div>
        </div>
      </div>

      <div className="datasetExtractContainer">
        <div className="datasetExtractContent">
          <div className="datasetExtractLeftContent">
            <div className="datasetExtractSection">
              <h2 className="datasetExtractSectionTitle">チャンク設定</h2>

              <div className="datasetExtractCard">
                <div className="datasetExtractCardHeader">
                  <div className="datasetExtractCardIcon blue">
                    <FiFileText />
                  </div>
                  <div className="datasetExtractCardTitle">汎用</div>
                </div>
                <div className="datasetExtractCardDescription">
                  汎用テキスト分割モードです。検索とコンテキスト抽出に同じチャンクを使用します。
                </div>

                <div className="datasetExtractFormGroup">
                  <div className="datasetExtractFormRow" style={{ display: 'flex', alignItems: 'center', marginBottom: '15px' }}>
                    <div style={{ marginRight: '15px' }}>
                      <div className="datasetExtractFormLabel">
                        チャンク識別子 <InfoTooltip content="区切り文字は、テキストを区切るために使用される文字です。\n\n と \n は、段落と行を区切るために一般的に使用される区切り記号です。" />
                      </div>
                      <div className="datasetExtractFormInput">
                        <input
                          type="text"
                          value={chunkIdentifier}
                          onChange={(e) => setChunkIdentifier(e.target.value)}
                          className="datasetExtractTextInput"
                          style={{ width: '180px' }}
                        />
                      </div>
                    </div>

                    <div style={{ marginRight: '15px' }}>
                      <div className="datasetExtractFormLabel">
                        最大チャンク長
                      </div>
                      <div className="datasetExtractFormInput">
                        <input
                          type="text"
                          value={chunkSize}
                          onChange={(e) => setChunkSize(e.target.value)}
                          className="datasetExtractTextInput"
                        />
                        <span className="datasetExtractInputSuffix">characters</span>
                      </div>
                    </div>

                    <div>
                      <div className="datasetExtractFormLabel">
                        チャンクのオーバーラップ <InfoTooltip content="連続するチャンク間で重複する文字数を指定します。" />
                      </div>
                      <div className="datasetExtractFormInput">
                        <input
                          type="text"
                          value={overlap}
                          onChange={(e) => setOverlap(e.target.value)}
                          className="datasetExtractTextInput"
                        />
                        <span className="datasetExtractInputSuffix">characters</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="datasetExtractFormGroup">
                  <div className="datasetExtractFormLabel">テキストの前処理ルール</div>
                  <div className="datasetExtractCheckboxGroup">
                    <label className="datasetExtractCheckboxLabel">
                      <input
                        type="checkbox"
                        checked={removeLineBreaks}
                        onChange={() => setRemoveLineBreaks(!removeLineBreaks)}
                        className="datasetExtractCheckbox"
                      />
                      連続するスペース、改行、タブを置換する
                    </label>

                    <label className="datasetExtractCheckboxLabel">
                      <input
                        type="checkbox"
                        checked={removeUrls}
                        onChange={() => setRemoveUrls(!removeUrls)}
                        className="datasetExtractCheckbox"
                      />
                      すべてのURLとメールアドレスを削除する
                    </label>
                  </div>
                </div>

                <div className="datasetExtractButtonGroup">
                  <button className="datasetExtractPreviewButton" onClick={handlePreview}>
                    <FaSearch /> チャンクをプレビュー
                  </button>
                  <button className="datasetExtractResetButton" onClick={handleReset}>
                    リセット
                  </button>
                </div>
              </div>
            </div>

            <div className="datasetExtractSection">
              <h2 className="datasetExtractSectionTitle">親子</h2>
              <div className="datasetExtractCard">
                <div className="datasetExtractCardHeader">
                  <div className="datasetExtractCardIcon orange">
                    <FiFileText />
                  </div>
                  <div className="datasetExtractCardTitle">親子</div>
                </div>
                <div className="datasetExtractCardDescription">
                  親子分割モードでは、子チャンクを検索に、親チャンクをコンテキスト抽出に使用します。
                </div>
              </div>
            </div>


            <div className="datasetExtractSection">
              <h2 className="datasetExtractSectionTitle">インデックス方法</h2>
              <div className="datasetExtractIndexMethods">
                <div className={`datasetExtractIndexCard ${indexMethod === 'highQuality' ? 'selected' : ''}`}
                     onClick={() => setIndexMethod('highQuality')}>
                  <div className="datasetExtractIndexCardHeader">
                    <div className="datasetExtractIndexCardIcon">
                      <FiSliders />
                    </div>
                    <div className="datasetExtractIndexCardTitle">
                      高品質 <RecommendBadge />
                    </div>
                  </div>
                  <div className="datasetExtractIndexCardDescription">
                    高品質なモデルを使用してドキュメントを処理し、より正確な検索結果を得ることができます。LLMが高品質の回答を生成するのに役立ちます。
                  </div>
                </div>

                <div className={`datasetExtractIndexCard ${indexMethod === 'economic' ? 'selected' : ''}`}
                     onClick={() => setIndexMethod('economic')}>
                  <div className="datasetExtractIndexCardHeader">
                    <div className="datasetExtractIndexCardIcon">
                      <IoMdSwitch />
                    </div>
                    <div className="datasetExtractIndexCardTitle">
                      経済的
                    </div>
                  </div>
                  <div className="datasetExtractIndexCardDescription">
                    検索用チャンクあたり10個のキーワードを使用することで、精度は下がりますが、トークン消費量を抑えられます。
                  </div>
                </div>
              </div>
            </div>

            <div className="datasetExtractSection">
              <h2 className="datasetExtractSectionTitle">埋め込みモデル</h2>
              <div className="datasetExtractModelSelector">
                <div
                  className="datasetExtractModelDisplay"
                  onClick={() => setShowModelDropdown(!showModelDropdown)}
                >
                  <div className="datasetExtractModelIcon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect width="24" height="24" rx="4" fill="#10B981" fillOpacity="0.1"/>
                      <path d="M12 6.5C8.96243 6.5 6.5 8.96243 6.5 12C6.5 15.0376 8.96243 17.5 12 17.5C15.0376 17.5 17.5 15.0376 17.5 12C17.5 8.96243 15.0376 6.5 12 6.5ZM5.5 12C5.5 8.41015 8.41015 5.5 12 5.5C15.5899 5.5 18.5 8.41015 18.5 12C18.5 15.5899 15.5899 18.5 12 18.5C8.41015 18.5 5.5 15.5899 5.5 12Z" fill="#10B981"/>
                      <path d="M12 8.5C10.067 8.5 8.5 10.067 8.5 12C8.5 13.933 10.067 15.5 12 15.5C13.933 15.5 15.5 13.933 15.5 12C15.5 10.067 13.933 8.5 12 8.5ZM7.5 12C7.5 9.51472 9.51472 7.5 12 7.5C14.4853 7.5 16.5 9.51472 16.5 12C16.5 14.4853 14.4853 16.5 12 16.5C9.51472 16.5 7.5 14.4853 7.5 12Z" fill="#10B981"/>
                      <path d="M12 10.5C11.1716 10.5 10.5 11.1716 10.5 12C10.5 12.8284 11.1716 13.5 12 13.5C12.8284 13.5 13.5 12.8284 13.5 12C13.5 11.1716 12.8284 10.5 12 10.5ZM9.5 12C9.5 10.6193 10.6193 9.5 12 9.5C13.3807 9.5 14.5 10.6193 14.5 12C14.5 13.3807 13.3807 14.5 12 14.5C10.6193 14.5 9.5 13.3807 9.5 12Z" fill="#10B981"/>
                    </svg>
                  </div>
                  <div className="datasetExtractModelName">{embeddingModel}</div>
                  <div className="datasetExtractModelArrow">
                    {showModelDropdown ? <FaChevronUp /> : <FaChevronDown />}
                  </div>
                </div>

                {showModelDropdown && (
                  <div className="datasetExtractModelDropdown">
                    <div className="datasetExtractModelSearchContainer">
                      <div className="datasetExtractModelSearchIcon">
                        <FaSearch />
                      </div>
                      <input
                        type="text"
                        placeholder="モデル検索"
                        className="datasetExtractModelSearchInput"
                      />
                    </div>

                    <div className="datasetExtractModelGroup">
                      <div className="datasetExtractModelGroupTitle">OpenAI</div>

                      <div
                        className={`datasetExtractModelOption ${embeddingModel === 'text-embedding-3-large' ? 'selected' : ''}`}
                        onClick={() => {
                          setEmbeddingModel('text-embedding-3-large');
                          setShowModelDropdown(false);
                        }}
                      >
                        <div className="datasetExtractModelOptionIcon">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="24" height="24" rx="4" fill="#10B981" fillOpacity="0.1"/>
                            <path d="M12 6.5C8.96243 6.5 6.5 8.96243 6.5 12C6.5 15.0376 8.96243 17.5 12 17.5C15.0376 17.5 17.5 15.0376 17.5 12C17.5 8.96243 15.0376 6.5 12 6.5ZM5.5 12C5.5 8.41015 8.41015 5.5 12 5.5C15.5899 5.5 18.5 8.41015 18.5 12C18.5 15.5899 15.5899 18.5 12 18.5C8.41015 18.5 5.5 15.5899 5.5 12Z" fill="#10B981"/>
                            <path d="M12 8.5C10.067 8.5 8.5 10.067 8.5 12C8.5 13.933 10.067 15.5 12 15.5C13.933 15.5 15.5 13.933 15.5 12C15.5 10.067 13.933 8.5 12 8.5ZM7.5 12C7.5 9.51472 9.51472 7.5 12 7.5C14.4853 7.5 16.5 9.51472 16.5 12C16.5 14.4853 14.4853 16.5 12 16.5C9.51472 16.5 7.5 14.4853 7.5 12Z" fill="#10B981"/>
                            <path d="M12 10.5C11.1716 10.5 10.5 11.1716 10.5 12C10.5 12.8284 11.1716 13.5 12 13.5C12.8284 13.5 13.5 12.8284 13.5 12C13.5 11.1716 12.8284 10.5 12 10.5ZM9.5 12C9.5 10.6193 10.6193 9.5 12 9.5C13.3807 9.5 14.5 10.6193 14.5 12C14.5 13.3807 13.3807 14.5 12 14.5C10.6193 14.5 9.5 13.3807 9.5 12Z" fill="#10B981"/>
                          </svg>
                        </div>
                        <div className="datasetExtractModelOptionName">text-embedding-3-large</div>
                        {embeddingModel === 'text-embedding-3-large' && (
                          <div className="datasetExtractModelOptionCheck">
                            <FaCheck />
                          </div>
                        )}
                      </div>

                      <div
                        className={`datasetExtractModelOption ${embeddingModel === 'text-embedding-3-small' ? 'selected' : ''}`}
                        onClick={() => {
                          setEmbeddingModel('text-embedding-3-small');
                          setShowModelDropdown(false);
                        }}
                      >
                        <div className="datasetExtractModelOptionIcon">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="24" height="24" rx="4" fill="#10B981" fillOpacity="0.1"/>
                            <path d="M12 6.5C8.96243 6.5 6.5 8.96243 6.5 12C6.5 15.0376 8.96243 17.5 12 17.5C15.0376 17.5 17.5 15.0376 17.5 12C17.5 8.96243 15.0376 6.5 12 6.5ZM5.5 12C5.5 8.41015 8.41015 5.5 12 5.5C15.5899 5.5 18.5 8.41015 18.5 12C18.5 15.5899 15.5899 18.5 12 18.5C8.41015 18.5 5.5 15.5899 5.5 12Z" fill="#10B981"/>
                            <path d="M12 8.5C10.067 8.5 8.5 10.067 8.5 12C8.5 13.933 10.067 15.5 12 15.5C13.933 15.5 15.5 13.933 15.5 12C15.5 10.067 13.933 8.5 12 8.5ZM7.5 12C7.5 9.51472 9.51472 7.5 12 7.5C14.4853 7.5 16.5 9.51472 16.5 12C16.5 14.4853 14.4853 16.5 12 16.5C9.51472 16.5 7.5 14.4853 7.5 12Z" fill="#10B981"/>
                            <path d="M12 10.5C11.1716 10.5 10.5 11.1716 10.5 12C10.5 12.8284 11.1716 13.5 12 13.5C12.8284 13.5 13.5 12.8284 13.5 12C13.5 11.1716 12.8284 10.5 12 10.5ZM9.5 12C9.5 10.6193 10.6193 9.5 12 9.5C13.3807 9.5 14.5 10.6193 14.5 12C14.5 13.3807 13.3807 14.5 12 14.5C10.6193 14.5 9.5 13.3807 9.5 12Z" fill="#10B981"/>
                          </svg>
                        </div>
                        <div className="datasetExtractModelOptionName">text-embedding-3-small</div>
                        {embeddingModel === 'text-embedding-3-small' && (
                          <div className="datasetExtractModelOptionCheck">
                            <FaCheck />
                          </div>
                        )}
                      </div>

                      <div
                        className={`datasetExtractModelOption ${embeddingModel === 'text-embedding-ada-002' ? 'selected' : ''}`}
                        onClick={() => {
                          setEmbeddingModel('text-embedding-ada-002');
                          setShowModelDropdown(false);
                        }}
                      >
                        <div className="datasetExtractModelOptionIcon">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="24" height="24" rx="4" fill="#10B981" fillOpacity="0.1"/>
                            <path d="M12 6.5C8.96243 6.5 6.5 8.96243 6.5 12C6.5 15.0376 8.96243 17.5 12 17.5C15.0376 17.5 17.5 15.0376 17.5 12C17.5 8.96243 15.0376 6.5 12 6.5ZM5.5 12C5.5 8.41015 8.41015 5.5 12 5.5C15.5899 5.5 18.5 8.41015 18.5 12C18.5 15.5899 15.5899 18.5 12 18.5C8.41015 18.5 5.5 15.5899 5.5 12Z" fill="#10B981"/>
                            <path d="M12 8.5C10.067 8.5 8.5 10.067 8.5 12C8.5 13.933 10.067 15.5 12 15.5C13.933 15.5 15.5 13.933 15.5 12C15.5 10.067 13.933 8.5 12 8.5ZM7.5 12C7.5 9.51472 9.51472 7.5 12 7.5C14.4853 7.5 16.5 9.51472 16.5 12C16.5 14.4853 14.4853 16.5 12 16.5C9.51472 16.5 7.5 14.4853 7.5 12Z" fill="#10B981"/>
                            <path d="M12 10.5C11.1716 10.5 10.5 11.1716 10.5 12C10.5 12.8284 11.1716 13.5 12 13.5C12.8284 13.5 13.5 12.8284 13.5 12C13.5 11.1716 12.8284 10.5 12 10.5ZM9.5 12C9.5 10.6193 10.6193 9.5 12 9.5C13.3807 9.5 14.5 10.6193 14.5 12C14.5 13.3807 13.3807 14.5 12 14.5C10.6193 14.5 9.5 13.3807 9.5 12Z" fill="#10B981"/>
                          </svg>
                        </div>
                        <div className="datasetExtractModelOptionName">text-embedding-ada-002</div>
                        {embeddingModel === 'text-embedding-ada-002' && (
                          <div className="datasetExtractModelOptionCheck">
                            <FaCheck />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="datasetExtractSection">
              <h2 className="datasetExtractSectionTitle">検索設定</h2>
              <div className="datasetExtractSearchSettings">
                <div className="datasetExtractSearchSettingsInfo">
                  <span>詳細は</span>
                  <a href="#" className="datasetExtractSearchSettingsLink">こちら</a>
                  <span>検索方法についての詳細については、いつでもナレッジベースの設定で変更できます。</span>
                </div>

                <div className="datasetExtractSearchBox">
                  {/* ベクトル検索 */}
                  <div className="datasetExtractVectorSearch">
                    <div className="datasetExtractVectorSearchIcon">
                      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect width="48" height="48" rx="8" fill="#7C3AED" fillOpacity="0.1"/>
                        <path d="M16 16H20V20H16V16Z" fill="#7C3AED"/>
                        <path d="M16 22H20V26H16V22Z" fill="#7C3AED"/>
                        <path d="M16 28H20V32H16V28Z" fill="#7C3AED"/>
                        <path d="M22 16H26V20H22V16Z" fill="#7C3AED"/>
                        <path d="M22 22H26V26H22V22Z" fill="#7C3AED"/>
                        <path d="M22 28H26V32H22V28Z" fill="#7C3AED"/>
                        <path d="M28 16H32V20H28V16Z" fill="#7C3AED"/>
                        <path d="M28 22H32V26H28V22Z" fill="#7C3AED"/>
                        <path d="M28 28H32V32H28V28Z" fill="#7C3AED"/>
                      </svg>
                    </div>
                    <div className="datasetExtractVectorSearchContent">
                      <h3 className="datasetExtractVectorSearchTitle">ベクトル検索</h3>
                      <p className="datasetExtractVectorSearchDescription">
                        クエリの埋め込みを生成し、そのベクトル表現に最も類似したテキストチャンクを検索します。
                      </p>
                    </div>
                  </div>

                  {/* 全文検索 */}
                  <div className="fullTextSearchContainer">
                    <div className="fullTextSearchHeader">
                      <div className="fullTextSearchIconContainer">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z" stroke="#7C3AED" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      <div className="fullTextSearchContent">
                        <h3>全文検索</h3>
                        <p>
                          ドキュメント内のすべての用語をインデックス化し、ユーザーが任意の用語を検索してそれに関連するテキストチャンクを取得できるようにします。
                        </p>
                      </div>
                    </div>

                    {/* Rerankモデル */}
                    <div className="rerankModelOption">
                      <label className="rerankModelLabel">
                        <input
                          type="checkbox"
                          checked={useRerank}
                          onChange={() => setUseRerank(!useRerank)}
                          className="rerankModelCheckbox"
                        />
                        <span className="rerankModelText">Rerankモデル</span>
                        <BsQuestionCircle className="rerankModelInfo" />
                      </label>
                    </div>

                    {/* トップKとスコア閾値 */}
                    <div className="searchControlsContainer">
                      {/* トップK */}
                      <div className="searchControlItem">
                        <div className="searchControlLabel">
                          トップK <BsQuestionCircle className="searchControlInfo" />
                        </div>
                        <div className="searchControlInputGroup">
                          <div className="numberInputContainer">
                            <div className="numberInputWrapper">
                              <input
                                type="number"
                                value={topK}
                                onChange={(e) => setTopK(parseInt(e.target.value))}
                                className="numberInput"
                                min="1"
                                max="20"
                              />
                              <div className="numberInputButtons">
                                <button
                                  className="numberInputButton"
                                  onClick={() => setTopK(Math.min(topK + 1, 20))}
                                >
                                  <FaChevronUp />
                                </button>
                                <button
                                  className="numberInputButton"
                                  onClick={() => setTopK(Math.max(topK - 1, 1))}
                                >
                                  <FaChevronDown />
                                </button>
                              </div>
                            </div>
                          </div>
                          <div className="sliderContainer">
                            <input
                              type="range"
                              min="1"
                              max="20"
                              value={topK}
                              onChange={(e) => setTopK(parseInt(e.target.value))}
                              className="customRangeSlider sliderInput"
                            />
                          </div>
                        </div>
                      </div>

                      {/* スコア閾値 */}
                      <div className="searchControlItem">
                        <div className="searchControlLabel">
                          スコア閾値 <BsQuestionCircle className="searchControlInfo" />
                        </div>
                        <div className="searchControlInputGroup">
                          <div className="numberInputContainer">
                            <div className="numberInputWrapper">
                              <input
                                type="number"
                                value={scoreThreshold}
                                onChange={(e) => setScoreThreshold(parseFloat(e.target.value))}
                                className="numberInput"
                                min="0"
                                max="1"
                                step="0.1"
                              />
                              <div className="numberInputButtons">
                                <button
                                  className="numberInputButton"
                                  onClick={() => setScoreThreshold(Math.min(scoreThreshold + 0.1, 1))}
                                >
                                  <FaChevronUp />
                                </button>
                                <button
                                  className="numberInputButton"
                                  onClick={() => setScoreThreshold(Math.max(scoreThreshold - 0.1, 0))}
                                >
                                  <FaChevronDown />
                                </button>
                              </div>
                            </div>
                          </div>
                          <div className="sliderContainer">
                            <input
                              type="range"
                              min="0"
                              max="1"
                              step="0.1"
                              value={scoreThreshold}
                              onChange={(e) => setScoreThreshold(parseFloat(e.target.value))}
                              className="customRangeSlider sliderInput"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>


          {/* 底部按钮区域 */}
          <div className="datasetExtractActions">
            <button className="datasetExtractSecondaryButton" onClick={handleBack}>
              <FaArrowLeft style={{ marginRight: '5px' }} /> 前のステップ
            </button>
            <button className="datasetExtractPrimaryButton" onClick={handleNext}>
              保存して次へ
            </button>
          </div>

          </div>

          <div className="datasetExtractRightContent">
            <div className="datasetExtractPreviewHeader">
              <h2 className="datasetExtractSectionTitle">プレビュー</h2>
              <div className="datasetExtractPreviewFile">
                <div className="datasetExtractPreviewFileIcon">
                  <FaFileAlt />
                </div>
                <div className="datasetExtractPreviewFileName">robots.txt</div>
                <div className="datasetExtractPreviewFileCount">検出チャンク数: 0</div>
              </div>
              <div className="datasetExtractPreviewContent">
                <div className="datasetExtractPreviewEmpty">
                  <div className="datasetExtractPreviewEmptyIcon">
                    <FaSearch />
                  </div>
                  <div className="datasetExtractPreviewEmptyText">
                    プレビューを表示するには、左側の「チャンクをプレビュー」ボタンをクリックしてください。
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>
  );
};

export default DatasetExtract;
