import React, { useState } from 'react';
import './css/logs.css';
import { FaSearch, FaChevronDown, FaCircle } from 'react-icons/fa';
import { BsCalendar3 } from 'react-icons/bs';

interface LogEntry {
  id: string;
  timestamp: string;
  status: 'SUCCESS' | 'ERROR';
  runtime: string;
  tokens: number;
  user: string;
}

const Logs = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [timeFilter, setTimeFilter] = useState('過去7日間');
  const [selectedStatus, setSelectedStatus] = useState<string>('SUCCESS');

  // サンプルログデータ
  const sampleLogs: LogEntry[] = [
    {
      id: '1',
      timestamp: '05/16/2023 05:46',
      status: 'SUCCESS',
      runtime: '0.116s',
      tokens: 0,
      user: 'd18fa0c6-6231-4395-acf7-f743333da3df'
    }
  ];

  const filteredLogs = sampleLogs.filter(log => {
    const matchesSearch = log.user.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'ALL' || log.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="logs-container">
      <div className="logs-header">
        <h1>ワークフローログ</h1>
        <p>このログは Automate の操作を記録しました。</p>
      </div>

      <div className="logs-filters">
        <div className="filter-group">
          <button className="filter-button">
            <FaChevronDown className="filter-icon" />
            <span>Success</span>
            <FaCircle className="status-indicator success" />
          </button>

          <button className="time-filter-button">
            <BsCalendar3 className="filter-icon" />
            <span>{timeFilter}</span>
          </button>

          <div className="search-container">
            <FaSearch className="search-icon" />
            <input
              type="text"
              className="search-input"
              placeholder="検索"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
      </div>

      <div className="logs-table-container">
        <table className="logs-table">
          <thead>
            <tr>
              <th className="column-timestamp">開始時間</th>
              <th className="column-status">ステータス</th>
              <th className="column-runtime">ランタイム</th>
              <th className="column-tokens">トークン</th>
              <th className="column-user">エンドユーザーまたはアカウント</th>
            </tr>
          </thead>
          <tbody>
            {filteredLogs.length > 0 ? (
              filteredLogs.map(log => (
                <tr key={log.id} className="log-row">
                  <td className="column-timestamp">
                    <span className="dot"></span>
                    {log.timestamp}
                  </td>
                  <td className="column-status">
                    <span className={`status-badge ${log.status.toLowerCase()}`}>
                      {log.status}
                    </span>
                  </td>
                  <td className="column-runtime">{log.runtime}</td>
                  <td className="column-tokens">{log.tokens}</td>
                  <td className="column-user">{log.user}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="no-logs">
                  ログが見つかりませんでした。検索条件を変更してください。
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Logs;
