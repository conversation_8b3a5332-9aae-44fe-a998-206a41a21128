import React, { useState } from 'react';
import './css/develop.css';
import { FaRegCopy, FaEllipsisH, FaTimes, FaPlus, FaClipboard, FaKey, FaTimes as FaClose } from 'react-icons/fa';

const Develop = () => {
  const apiUrl = 'https://api.dify.ai/v1';
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [showNewKeyModal, setShowNewKeyModal] = useState(false);
  const [apiKeys, setApiKeys] = useState<Array<{key: string, created: string, lastUsed: string | null}>>([]);
  const [newApiKey, setNewApiKey] = useState('');

  const handleCreateNewKey = () => {
    // 生成一个随机的 API 密钥
    const randomKey = `app-${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;
    setNewApiKey(randomKey);

    // 添加到密钥列表
    const now = new Date();
    const formattedDate = `${now.getMonth() + 1}/${now.getDate()}/${now.getFullYear()} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')} ${now.getHours() >= 12 ? 'PM' : 'AM'}`;

    setApiKeys([...apiKeys, {
      key: randomKey,
      created: formattedDate,
      lastUsed: null
    }]);

    setShowApiKeyModal(false);
    setShowNewKeyModal(true);
  };

  const handleCloseNewKeyModal = () => {
    setShowNewKeyModal(false);
    setShowApiKeyModal(true);
  };

  const handleCopyApiKey = () => {
    navigator.clipboard.writeText(newApiKey);
    // 可以添加一个复制成功的提示
  };

  const [showSidebar, setShowSidebar] = useState(true);

  const toggleSidebar = () => {
    setShowSidebar(!showSidebar);
  };

  const sidebarItems = [
    { id: 'execute', label: 'ワークフローを実行' },
    { id: 'details', label: 'ワークフロー実行詳細を取得' },
    { id: 'stop', label: '生成を停止' },
    { id: 'upload', label: 'ファイルアップロード' },
    { id: 'logs', label: 'ワークフローログを取得' },
    { id: 'info', label: 'アプリケーションの基本情報を取得' },
    { id: 'params', label: 'アプリケーションのパラメータ情報を取得' },
    { id: 'webapp', label: 'アプリのWebApp設定を取得' }
  ];

  return (
    <div className="develop-container">
      {showSidebar && (
        <div className="content-sidebar">
          <div className="sidebar-header">
            <h3>内容</h3>
            <button className="close-sidebar-button" onClick={toggleSidebar}>
              <FaClose />
            </button>
          </div>
          <ul className="sidebar-menu">
            {sidebarItems.map(item => (
              <li key={item.id} className="sidebar-item">
                <a href={`#${item.id}`}>{item.label}</a>
              </li>
            ))}
          </ul>
        </div>
      )}
      <div className="main-content">
        <div className="api-header">
        <div className="api-server-info">
          <span className="api-label">APIサーバー</span>
          <span className="api-url">{apiUrl}</span>
          <button className="copy-button"><FaRegCopy /></button>
        </div>
        <div className="api-status">
          <span className="status-badge">稼働中</span>
          <button className="api-key-button" onClick={() => setShowApiKeyModal(true)}>
            <FaKey className="key-icon" />
            <span>APIキー</span>
          </button>
        </div>
      </div>

      <div className="develop-header">
        <h1>ワークフローアプリAPI</h1>
        <p>ワークフローアプリケーションは、セクションをサポートせず、翻訳、記事作成、要約などに最適です。</p>
        <button className="menu-button"><FaEllipsisH /></button>
      </div>

      <div className="divider"></div>

      <div className="develop-section">
        <h2>ベースURL</h2>
        <div className="code-block">
          <div className="code-header">
            <span>コード</span>
          </div>
          <div className="code-content">
            <pre>{apiUrl}</pre>
          </div>
        </div>
      </div>

      <div className="divider"></div>

      <div className="develop-section">
        <h2>認証</h2>
        <p>
          サービスAPIは <code>API-Key</code> 認証を使用します。
          <strong>APIキーの漏洩を防ぐため、APIキーはクライアント側で共有せず、サーバー側で保存することを強くお勧めします。</strong>
        </p>
        <p>すべてのAPIリクエストにおいて、以下のように <code>Authorization</code> HTTPヘッダーにAPIキーを含めてください：</p>

        <div className="code-block">
          <div className="code-header">
            <span>コード</span>
          </div>
          <div className="code-content">
            <pre>Authorization: Bearer {'{API_KEY}'}</pre>
          </div>
        </div>
      </div>

      <div className="divider"></div>

      <div className="develop-section">
        <h2>APIエンドポイント</h2>

        <div className="endpoint">
          <h3>メッセージの送信</h3>
          <div className="endpoint-details">
            <div className="endpoint-method">POST</div>
            <div className="endpoint-path">/chat-messages</div>
          </div>
          <p>ユーザーメッセージを送信し、AIからの応答を取得します。</p>

          <h4>リクエストパラメータ</h4>
          <div className="params-table">
            <div className="param-row header">
              <div className="param-name">パラメータ</div>
              <div className="param-type">タイプ</div>
              <div className="param-required">必須</div>
              <div className="param-desc">説明</div>
            </div>
            <div className="param-row">
              <div className="param-name">query</div>
              <div className="param-type">string</div>
              <div className="param-required">はい</div>
              <div className="param-desc">ユーザーからの質問やメッセージ</div>
            </div>
            <div className="param-row">
              <div className="param-name">conversation_id</div>
              <div className="param-type">string</div>
              <div className="param-required">いいえ</div>
              <div className="param-desc">会話ID（新しい会話の場合は省略可能）</div>
            </div>
            <div className="param-row">
              <div className="param-name">user</div>
              <div className="param-type">string</div>
              <div className="param-required">いいえ</div>
              <div className="param-desc">ユーザー識別子</div>
            </div>
          </div>

          <h4>レスポンス</h4>
          <div className="code-block">
            <div className="code-header">
              <span>レスポンス例</span>
            </div>
            <div className="code-content">
              <pre>{`{
  "conversation_id": "conv_123456789",
  "message_id": "msg_987654321",
  "answer": "こんにちは！お手伝いできることがあれば教えてください。",
  "created_at": 1620000000
}`}</pre>
            </div>
          </div>
        </div>

        <div className="endpoint">
          <h3>会話履歴の取得</h3>
          <div className="endpoint-details">
            <div className="endpoint-method">GET</div>
            <div className="endpoint-path">/conversations/{'{conversation_id}'}/messages</div>
          </div>
          <p>特定の会話の履歴メッセージを取得します。</p>

          <h4>パスパラメータ</h4>
          <div className="params-table">
            <div className="param-row header">
              <div className="param-name">パラメータ</div>
              <div className="param-type">タイプ</div>
              <div className="param-required">必須</div>
              <div className="param-desc">説明</div>
            </div>
            <div className="param-row">
              <div className="param-name">conversation_id</div>
              <div className="param-type">string</div>
              <div className="param-required">はい</div>
              <div className="param-desc">取得する会話のID</div>
            </div>
          </div>

          <h4>レスポンス</h4>
          <div className="code-block">
            <div className="code-header">
              <span>レスポンス例</span>
            </div>
            <div className="code-content">
              <pre>{`{
  "conversation_id": "conv_123456789",
  "messages": [
    {
      "message_id": "msg_123456789",
      "role": "user",
      "content": "こんにちは",
      "created_at": 1620000000
    },
    {
      "message_id": "msg_987654321",
      "role": "assistant",
      "content": "こんにちは！お手伝いできることがあれば教えてください。",
      "created_at": 1620000010
    }
  ]
}`}</pre>
            </div>
          </div>
        </div>
      </div>

      {/* API キーモーダル */}
      {showApiKeyModal && (
        <div className="modal-overlay">
          <div className="modal-container">
            <div className="modal-header">
              <h2>APIシークレットキー</h2>
              <button className="close-button" onClick={() => setShowApiKeyModal(false)}>
                <FaTimes />
              </button>
            </div>
            <div className="modal-content">
              <p className="modal-description">
                APIの悪用を防ぐために、APIキーを保護してください。フロントエンドのコードで平文として使用しないでください。 :)
              </p>

              {apiKeys.length > 0 && (
                <div className="api-keys-table">
                  <div className="table-header">
                    <div className="key-column">シークレットキー</div>
                    <div className="date-column">作成日時</div>
                    <div className="date-column">最終使用日時</div>
                    <div className="actions-column"></div>
                  </div>

                  {apiKeys.map((key, index) => (
                    <div className="table-row" key={index}>
                      <div className="key-column">{key.key.substring(0, 10)}...{key.key.substring(key.key.length - 10)}</div>
                      <div className="date-column">{key.created}</div>
                      <div className="date-column">{key.lastUsed || 'なし'}</div>
                      <div className="actions-column">
                        <button className="icon-button"><FaRegCopy /></button>
                        <button className="icon-button delete-button"><FaTimes /></button>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              <div className="modal-footer">
                <button className="create-key-button" onClick={handleCreateNewKey}>
                  <FaPlus />
                  <span>新しいシークレットキーを作成</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 新しいAPIキーモーダル */}
      {showNewKeyModal && (
        <div className="modal-overlay">
          <div className="modal-container">
            <div className="modal-header">
              <h2>APIシークレットキー</h2>
              <button className="close-button" onClick={handleCloseNewKeyModal}>
                <FaTimes />
              </button>
            </div>
            <div className="modal-content">
              <p className="modal-description">
                このキーを安全でアクセス可能な場所に保管してください。
              </p>

              <div className="api-key-display">
                <div className="api-key-value">{newApiKey}</div>
                <button className="copy-key-button" onClick={handleCopyApiKey}>
                  <FaClipboard />
                </button>
              </div>

              <div className="modal-footer center">
                <button className="ok-button" onClick={handleCloseNewKeyModal}>
                  OK
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
};

export default Develop;
