/* 工作流节点状态样式 */


/* 基础节点样式覆盖 */


/* .react-flow__node {
    transition: all 0.3s ease !important;
} */


/* 强制覆盖所有节点的背景色 */


/* .react-flow__node * {
    background-color: inherit !important;
} */


/* 待执行状态 */

.node-pending {
    background-color: #ffffff !important;
    border: 2px solid #e0e0e0 !important;
}

.react-flow__node.node-pending {
    background-color: #ffffff !important;
    border: 2px solid #e0e0e0 !important;
}

.react-flow__node.node-pending>div {
    background-color: #ffffff !important;
}


/* 确保所有节点类型的PENDING状态都有白色背景 */

.react-flow__node-startNode.node-pending,
.react-flow__node-endNode.node-pending,
.react-flow__node-llmNode.node-pending,
.react-flow__node-datasetGrepNode.node-pending,
.react-flow__node-httpRequestNode.node-pending {
    background-color: #ffffff !important;
    border: 2px solid #e0e0e0 !important;
}

.react-flow__node-startNode.node-pending>div,
.react-flow__node-endNode.node-pending>div,
.react-flow__node-llmNode.node-pending>div,
.react-flow__node-datasetGrepNode.node-pending>div,
.react-flow__node-httpRequestNode.node-pending>div {
    background-color: #ffffff !important;
}


/* 执行中状态 */

.node-running {
    background-color: #e8f5e8 !important;
    border: 2px solid #4caf50 !important;
}

.react-flow__node.node-running {
    background-color: #e8f5e8 !important;
    border: 2px solid #4caf50 !important;
}

.react-flow__node.node-running>div {
    background-color: #e8f5e8 !important;
}


/* 成功状态 */

.node-success {
    background-color: #e3f2fd !important;
    border: 2px solid #2196f3 !important;
}

.react-flow__node.node-success {
    background-color: #e3f2fd !important;
    border: 2px solid #2196f3 !important;
}

.react-flow__node.node-success>div {
    background-color: #e3f2fd !important;
}


/* 失败状态 */

.node-failed {
    background-color: #ffebee !important;
    border: 2px solid #f44336 !important;
}

.react-flow__node.node-failed {
    background-color: #ffebee !important;
    border: 2px solid #f44336 !important;
}

.react-flow__node.node-failed>div {
    background-color: #ffebee !important;
}


/* 脉冲动画已删除 */


/* 强制禁用所有动画 */

.node-running,
.react-flow__node.node-running,
.react-flow__node-startNode.node-running,
.react-flow__node-endNode.node-running,
.react-flow__node-llmNode.node-running,
.react-flow__node-datasetGrepNode.node-running,
.react-flow__node-httpRequestNode.node-running {
    animation: none !important;
    box-shadow: none !important;
}


/* 强制覆盖所有可能的节点样式 */


/* .react-flow__node[class*="node-"] {
    transition: all 0.3s ease !important;
} */


/* .react-flow__node[class*="node-"]>div,
.react-flow__node[class*="node-"]>div>div {
    background-color: inherit !important;
    border: inherit !important;
} */


/* 特定节点类型的样式覆盖 */

.react-flow__node-startNode.node-running,
.react-flow__node-endNode.node-running,
.react-flow__node-llmNode.node-running,
.react-flow__node-datasetGrepNode.node-running,
.react-flow__node-httpRequestNode.node-running {
    background-color: #e8f5e8 !important;
    border: 2px solid #4caf50 !important;
}

.react-flow__node-startNode.node-success,
.react-flow__node-endNode.node-success,
.react-flow__node-llmNode.node-success,
.react-flow__node-datasetGrepNode.node-success,
.react-flow__node-httpRequestNode.node-success {
    background-color: #e3f2fd !important;
    border: 2px solid #2196f3 !important;
}

.react-flow__node-startNode.node-failed,
.react-flow__node-endNode.node-failed,
.react-flow__node-llmNode.node-failed,
.react-flow__node-datasetGrepNode.node-failed,
.react-flow__node-httpRequestNode.node-failed {
    background-color: #ffebee !important;
    border: 2px solid #f44336 !important;
}


/* 确保内部元素也继承背景色 */

.react-flow__node-startNode.node-running>div,
.react-flow__node-endNode.node-running>div,
.react-flow__node-llmNode.node-running>div,
.react-flow__node-datasetGrepNode.node-running>div,
.react-flow__node-httpRequestNode.node-running>div {
    background-color: #e8f5e8 !important;
}

.react-flow__node-startNode.node-success>div,
.react-flow__node-endNode.node-success>div,
.react-flow__node-llmNode.node-success>div,
.react-flow__node-datasetGrepNode.node-success>div,
.react-flow__node-httpRequestNode.node-success>div {
    background-color: #e3f2fd !important;
}

.react-flow__node-startNode.node-failed>div,
.react-flow__node-endNode.node-failed>div,
.react-flow__node-llmNode.node-failed>div,
.react-flow__node-datasetGrepNode.node-failed>div,
.react-flow__node-httpRequestNode.node-failed>div {
    background-color: #ffebee !important;
}