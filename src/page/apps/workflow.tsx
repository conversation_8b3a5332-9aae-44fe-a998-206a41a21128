import React, { useCallback, useState, useEffect, useRef } from 'react';

import {
  ReactFlow,
  MiniMap,
  Background,
  useNodesState,
  useEdgesState,
  applyNodeChanges,
  applyEdgeChanges,
  addEdge,
  Panel,
  ReactFlowInstance,
  Node,
  Edge
} from '@xyflow/react';

import Header from '../../components/header.tsx';
import Toolbar from '../../components/tools.tsx';
import AddNode from '../../components/addNode.tsx';
import StartNode from '../../nodes/StartNode.tsx';
import EndNode from '../../nodes/EndNode.tsx';
import LLMNode from '../../nodes/LLMNode.tsx';
import DatasetGrepNode from '../../nodes/DatasetGrepNode.tsx';
import CodeNode from '../../nodes/CodeNode.tsx';
import HttpRequestNode from '../../nodes/HttpRequestNode.tsx';
import StartNodeDetailPanel from '../../nodes/StartNodeDetailPanel.tsx';
import EndNodeDetailPanel from '../../nodes/EndNodeDetailPanel.tsx';
import LLMNodeDetailPanel from '../../nodes/LLMNodeDetailPanel.tsx';
import DatasetGrepNodeDetailPanel from '../../nodes/DatasetGrepNodeDetailPanel.tsx';
import CodeNodeDetailPanel from '../../nodes/CodeNodeDetailPanel.tsx';
import HttpRequestNodeDetailPanel from '../../nodes/HttpRequestNodeDetailPanel.tsx';
import { NodeEventProvider, useNodeEvents } from '../../utils/NodeEventContext';
import { AppResponse } from '../../services/appService';
import { showInfo } from '../../utils/toast';

import '@xyflow/react/dist/style.css';
import './css/Workflow.css';

const initBgColor = '#f5f5f5';

// 定义节点类型
const nodeTypes = {
  startNode: StartNode,
  endNode: EndNode,
  llmNode: LLMNode,
  datasetGrepNode: DatasetGrepNode,
  codeNode: CodeNode,
  httpRequestNode: HttpRequestNode,
};

// 定义组件属性类型
interface WorkflowProps {
  appInfo: AppResponse | null;
}

// 内部组件，用于处理节点详情面板
const WorkflowContent: React.FC<WorkflowProps> = ({ appInfo }) => {
  const flowKey = 'example-flow';
  const getNodeId = () => `randomnode_${+new Date()}`;

  const [rfInstance, setRfInstance] = useState<ReactFlowInstance | null>(null);

  // 空的初始节点和边
  const initialNodes: Node[] = [];
  const initialEdges: Edge[] = [];

  const [nodes, setNodes] = useNodesState(initialNodes);
  const [edges, setEdges] = useEdgesState(initialEdges);
  const [bgColor] = useState(initBgColor);

  // 使用useRef来跟踪是否已经显示过恢复成功的消息
  const hasShownRestoreMessage = useRef<{[key: string]: boolean}>({});

  // 使用节点事件上下文
  const { selectedNode, clearSelectedNode } = useNodeEvents();

  // 添加调试信息
  useEffect(() => {
    console.log('Current selected node (WorkflowContent):', selectedNode);
  }, [selectedNode]);

  // 处理flow_json恢复
  useEffect(() => {
    if (appInfo && appInfo.flow_json && appInfo.flow_json.trim() !== '') {
      try {
        // 检查是否已经为当前应用ID显示过恢复成功的消息
        const appId = appInfo.app_id;
        if (hasShownRestoreMessage.current[appId]) {
          console.log(`Restore message already shown for app_id: ${appId}, skipping...`);
          return;
        }

        console.log('Restoring flow from flow_json:', appInfo.flow_json);

        // 尝试解析flow_json
        const flowData = JSON.parse(appInfo.flow_json);

        if (flowData.nodes && flowData.edges) {
          // 恢复节点和边
          setNodes(flowData.nodes);
          setEdges(flowData.edges);

          // 显示恢复成功的消息，并标记为已显示
          // showInfo('ワークフローが正常に復元されました');
          hasShownRestoreMessage.current[appId] = true;
        }
      } catch (error) {
        console.error('Failed to parse flow_json:', error);
      }
    }
  }, [appInfo, setNodes, setEdges]);

  const onNodesChange = useCallback(
    (changes: any) => setNodes((nds) => applyNodeChanges(changes, nds)),
    [],
  );

  const onEdgesChange = useCallback(
    (changes: any) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    [],
  );

  const onConnect = useCallback(
    (params: any) => setEdges((eds) => addEdge(params, eds)),
    [setEdges],
  );

  // 轮询工作流状态
  const pollWorkflowStatus = useCallback(async (workflowRunId: string) => {
    if (!appInfo?.app_id) {
      console.log('No app_id available for polling');
      return;
    }

    try {
      console.log(`Polling workflow status for app_id: ${appInfo.app_id}, workflow_run_id: ${workflowRunId}`);
      const status = await getWorkflowStatus(appInfo.app_id, workflowRunId);
      console.log('Workflow status response:', status);
      console.log('Node details from API:', status.node_details);

      // 更新节点状态
      const newNodeStatuses: {[nodeId: string]: NodeStatus} = {};
      status.node_details.forEach(nodeDetail => {
        newNodeStatuses[nodeDetail.node_id] = nodeDetail;
        console.log(`Node ${nodeDetail.node_id} status: ${nodeDetail.status}`);
      });
      setNodeStatuses(newNodeStatuses);

      console.log('Current nodes before update:', nodes.map(n => ({ id: n.id, type: n.type })));

      // 更新节点视觉状态
      setNodes(currentNodes => {
        const updatedNodes = currentNodes.map(node => {
          const nodeStatus = newNodeStatuses[node.id];
          console.log(`Checking node ${node.id}, found status:`, nodeStatus);

          if (nodeStatus) {
            const backgroundColor = getNodeBackgroundColor(nodeStatus.status);
            const className = `node-${nodeStatus.status.toLowerCase()}`;
            console.log(`Updating node ${node.id} with status ${nodeStatus.status}, backgroundColor: ${backgroundColor}, className: ${className}`);

            return {
              ...node,
              data: {
                ...node.data,
                status: nodeStatus.status
              },
              style: {
                ...node.style,
                backgroundColor: backgroundColor,
                border: `2px solid ${nodeStatus.status === 'SUCCESS' ? '#2196f3' : nodeStatus.status === 'RUNNING' ? '#4caf50' : nodeStatus.status === 'FAILED' ? '#f44336' : '#e0e0e0'}`
              },
              className: className
            };
          }
          return node;
        });

        console.log('Updated nodes:', updatedNodes.map(n => ({
          id: n.id,
          status: n.data?.status,
          backgroundColor: n.style?.backgroundColor,
          className: n.className
        })));

        return updatedNodes;
      });

      // 如果工作流完成，停止轮询
      if (status.status === 'completed' || status.status === 'failed') {
        console.log(`Workflow ${status.status}, stopping polling`);
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
        setCurrentWorkflowRunId(null);

        if (status.status === 'completed') {
          showInfo('ワークフローが正常に完了しました');
        } else {
          showInfo('ワークフローが失敗しました');
        }
      }
    } catch (error) {
      console.error('Failed to poll workflow status:', error);
    }
  }, [appInfo?.app_id, setNodes]);

  // 获取节点背景颜色
  const getNodeBackgroundColor = (status: string): string => {
    switch (status) {
      case 'RUNNING':
        return '#e8f5e8'; // 浅绿色
      case 'SUCCESS':
        return '#e3f2fd'; // 浅蓝色
      case 'FAILED':
        return '#ffebee'; // 红色
      case 'PENDING':
      default:
        return '#ffffff'; // 白色
    }
  };

  // 开始轮询工作流状态
  const startPolling = useCallback((workflowRunId: string) => {
    console.log('Starting workflow status polling for:', workflowRunId);
    setCurrentWorkflowRunId(workflowRunId);

    // 清除之前的轮询
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    // 立即执行一次
    pollWorkflowStatus(workflowRunId);

    // 每2秒轮询一次
    pollingIntervalRef.current = setInterval(() => {
      pollWorkflowStatus(workflowRunId);
    }, 2000);
  }, [pollWorkflowStatus]);

  // 清理轮询
  useEffect(() => {
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, []);

  return (
    <div className="workflow-container">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        className="react-flow"
        onInit={setRfInstance}
        style={{ backgroundColor: bgColor }}
      >
        <Background />
        <Panel position="bottom-right" style={{ bottom: '30px' }}>
          <MiniMap />
        </Panel>

        <Panel position="bottom-left" style={{ bottom: '32px' }}>
          <Toolbar />
        </Panel>
        <Panel position="bottom-center" style={{ bottom: '45px' }}>
          <AddNode />
        </Panel>
        <Panel position="top-right">
          <Header
            rfInstance={rfInstance}
            flowKey={flowKey}
            setNodes={setNodes}
            setEdges={setEdges}
            getNodeId={getNodeId}
            onWorkflowRun={startPolling}
          />
        </Panel>
      </ReactFlow>

      {/* 显示节点详情面板 */}
      {selectedNode && (
        <div>
          {selectedNode.type === 'startNode' && (
            <StartNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
          {selectedNode.type === 'endNode' && (
            <EndNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
          {selectedNode.type === 'llmNode' && (
            <LLMNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
          {selectedNode.type === 'datasetGrepNode' && (
            <DatasetGrepNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
          {selectedNode.type === 'codeNode' && (
            <CodeNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
          {selectedNode.type === 'httpRequestNode' && (
            <HttpRequestNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
        </div>
      )}
    </div>
  );
};

// 主组件，提供上下文
const Workflow: React.FC<WorkflowProps> = ({ appInfo }) => {
  return (
    <NodeEventProvider>
      <WorkflowContent appInfo={appInfo} />
    </NodeEventProvider>
  );
};

export default Workflow;
