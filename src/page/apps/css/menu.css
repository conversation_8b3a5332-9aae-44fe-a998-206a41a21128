.menuContainer {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 240px;
    background-color: #ffffff;
    border-right: 1px solid #e0e0e0;
    font-family: sans-serif;
    transition: width 0.3s ease-in-out;
}

.menuContainer.compact {
    width: 64px;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
}

.headerInfo {
    display: flex;
    align-items: center;
}

.headerIcon {
    font-size: 28px;
    /* Increased size */
    color: #5856d6;
    /* Robot icon color from image */
    margin-right: 12px;
}

.headerIconImage {
    width: 28px;
    height: 28px;
    object-fit: cover;
    border-radius: 4px;
    margin-right: 12px;
}

.headerText {
    display: flex;
    flex-direction: column;
}

.title {
    font-size: 12px;
    font-weight: bold;
    color: #000000;
}

.subtitle {
    font-size: 12px;
    color: #8e8e93;
}

.optionsIcon {
    font-size: 20px;
    color: #8e8e93;
    cursor: pointer;
}

.menuList {
    list-style-type: none;
    padding: 8px 0;
    margin: 0;
    flex-grow: 1;
}

.menuItem {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    font-size: 14px;
    color: #3c3c43;
    cursor: pointer;
    border-radius: 6px;
    margin: 4px 8px;
    transition: margin 0.3s ease-in-out;
}

.menuItem.compact {
    margin: 4px 0;
    max-width: 48px;
    overflow: hidden;
}

.menuItem.compact .menuIcon {
    margin-right: 0;
}

.menuItem:hover {
    background-color: #f2f2f7;
}

.menuItem.active {
    background-color: #eef2ff;
    /* Light blue background for active item */
    color: #5e7cff;
    /* Blue text for active item */
    font-weight: 500;
}

.menuIcon {
    font-size: 18px;
    margin-right: 12px;
    color: inherit;
    /* Icon inherits color from parent li */
}

.menuItem.active .menuIcon {
    color: #5e7cff;
    /* Blue icon for active item */
}

.footer {
    padding: 16px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    /* Align to the left */
}

.footerIcon {
    font-size: 24px;
    color: #8e8e93;
    cursor: pointer;
}