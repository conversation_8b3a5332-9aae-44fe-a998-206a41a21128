/* 工作流应用特定样式 */


/* 主应用容器 */

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    /* background-color: var(--light-background); */
}


/* 内容区域 */

.content-container {
    display: flex;
    flex: 1;
    overflow: hidden;
}


/* 主工作区 */

.workspace {
    flex-grow: 1;
    position: relative;
    height: 100%;
    overflow: hidden;
}


/* 工作流容器 */

.workflow-container {
    flex-grow: 1;
    position: relative;
    flex-direction: column;
    height: 100vh;
    width: 100%;
}


/* 节点样式 */

.workflow-node {
    border: 1px solid var(--border-color);
    padding: 10px;
    border-radius: 8px;
    background-color: var(--background-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease;
}

.workflow-node:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}


/* 面板样式 */

.panel {
    background-color: var(--background-color);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 12px;
    margin: 8px;
}


/* 工具栏样式 */

.toolbar {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background-color: var(--background-color);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}


/* 动画 */

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}


/* 节点状态样式 - 已移动到 workflow-status.css */