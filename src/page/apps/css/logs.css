/* 日志页面样式 */

.logs-container {
    width: 100%;
    max-width: 1200px;
    margin: 0;
    padding: 20px;
    color: #333;
    background-color: #fff;
    overflow-y: auto;
    height: 100%;
}

.logs-header {
    margin-bottom: 20px;
    position: relative;
}

.logs-header h1 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.logs-header p {
    color: #666;
    font-size: 13px;
    line-height: 1.5;
}

.logs-filters {
    display: flex;
    margin-bottom: 15px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
}

.filter-button {
    display: flex;
    align-items: center;
    gap: 5px;
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
    color: #333;
    cursor: pointer;
    position: relative;
    height: 38px;
}

.filter-icon {
    font-size: 16px;
}

.status-indicator {
    font-size: 10px;
    margin-left: 8px;
}

.status-indicator.success {
    color: #22c55e;
}

.status-indicator.error {
    color: #ef4444;
}

.time-filter-button {
    display: flex;
    align-items: center;
    gap: 5px;
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
    color: #333;
    cursor: pointer;
    height: 38px;
}

.search-container {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 0 12px;
    width: 400px;
    height: 38px;
}

.search-icon {
    color: #999;
    font-size: 16px;
    margin-right: 8px;
}

.search-input {
    border: none;
    background: transparent;
    padding: 0;
    width: 100%;
    font-size: 13px;
    color: #333;
    outline: none;
    height: 100%;
}

.logs-table-container {
    width: 100%;
    overflow-x: auto;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.logs-table {
    width: 100%;
    border-collapse: collapse;
}

.logs-table thead {
    background-color: #f5f5f5;
}

.logs-table th {
    padding: 10px 15px;
    text-align: left;
    font-weight: 500;
    font-size: 13px;
    color: #666;
    border-bottom: 1px solid #e0e0e0;
}

.logs-table td {
    padding: 10px 15px;
    font-size: 13px;
    border-bottom: 1px solid #e0e0e0;
}

.logs-table tr:last-child td {
    border-bottom: none;
}

.column-timestamp {
    width: 160px;
    position: relative;
}

.column-status {
    width: 100px;
}

.column-runtime {
    width: 100px;
}

.column-tokens {
    width: 110px;
}

.column-user {
    min-width: 260px;
}

.dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #4299e1;
    border-radius: 50%;
    margin-right: 10px;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.success {
    background-color: #e6f7e6;
    color: #22c55e;
}

.status-badge.error {
    background-color: #fee2e2;
    color: #ef4444;
}

.no-logs {
    padding: 30px;
    text-align: center;
    color: #999;
}