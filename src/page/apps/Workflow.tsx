import React, { useCallback, useState, useEffect, useRef } from 'react';

import {
  ReactFlow,
  MiniMap,
  Background,
  useNodesState,
  useEdgesState,
  applyNodeChanges,
  applyEdgeChanges,
  addEdge,
  Panel,
  ReactFlowInstance,
  Node,
  Edge
} from '@xyflow/react';

import Header from '../../components/header.tsx';
import Toolbar from '../../components/tools.tsx';
import AddNode from '../../components/addNode.tsx';
import StartNode from '../../nodes/StartNode.tsx';
import EndNode from '../../nodes/EndNode.tsx';
import LLMNode from '../../nodes/LLMNode.tsx';
import DatasetGrepNode from '../../nodes/DatasetGrepNode.tsx';
import CodeNode from '../../nodes/CodeNode.tsx';
import HttpRequestNode from '../../nodes/HttpRequestNode.tsx';
import StartNodeDetailPanel from '../../nodes/StartNodeDetailPanel.tsx';
import EndNodeDetailPanel from '../../nodes/EndNodeDetailPanel.tsx';
import LLMNodeDetailPanel from '../../nodes/LLMNodeDetailPanel.tsx';
import DatasetGrepNodeDetailPanel from '../../nodes/DatasetGrepNodeDetailPanel.tsx';
import CodeNodeDetailPanel from '../../nodes/CodeNodeDetailPanel.tsx';
import HttpRequestNodeDetailPanel from '../../nodes/HttpRequestNodeDetailPanel.tsx';
import { NodeEventProvider, useNodeEvents } from '../../utils/NodeEventContext';
import { AppResponse, getWorkflowStatus, NodeStatus, WorkflowStatusResponse } from '../../services/appService';
import { showInfo } from '../../utils/toast';
import './workflow-status.css';

import '@xyflow/react/dist/style.css';
import './css/Workflow.css';

const initBgColor = '#f5f5f5';

// 定义节点类型
const nodeTypes = {
  startNode: StartNode,
  endNode: EndNode,
  llmNode: LLMNode,
  datasetGrepNode: DatasetGrepNode,
  codeNode: CodeNode,
  httpRequestNode: HttpRequestNode,
};

// 定义组件属性类型
interface WorkflowProps {
  appInfo: AppResponse | null;
}

// 内部组件，用于处理节点详情面板
const WorkflowContent: React.FC<WorkflowProps> = ({ appInfo }) => {
  const flowKey = 'example-flow';
  const getNodeId = () => `randomnode_${+new Date()}`;

  const [rfInstance, setRfInstance] = useState<ReactFlowInstance | null>(null);

  // 空的初始节点和边
  const initialNodes: Node[] = [];
  const initialEdges: Edge[] = [];

  const [nodes, setNodes] = useNodesState(initialNodes);
  const [edges, setEdges] = useEdgesState(initialEdges);
  const [bgColor] = useState(initBgColor);

  // 使用useRef来跟踪是否已经显示过恢复成功的消息
  const hasShownRestoreMessage = useRef<{[key: string]: boolean}>({});

  // 工作流运行状态管理
  const [currentWorkflowRunId, setCurrentWorkflowRunId] = useState<string | null>(null);
  const [nodeStatuses, setNodeStatuses] = useState<{[nodeId: string]: NodeStatus}>({});
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 使用节点事件上下文
  const { selectedNode, clearSelectedNode } = useNodeEvents();

  // 添加调试信息
  useEffect(() => {
    console.log('Current selected node (WorkflowContent):', selectedNode);
  }, [selectedNode]);

  // 处理flow_json恢复
  useEffect(() => {
    if (appInfo && appInfo.flow_json && appInfo.flow_json.trim() !== '') {
      try {
        // 检查是否已经为当前应用ID显示过恢复成功的消息
        const appId = appInfo.app_id;
        if (hasShownRestoreMessage.current[appId]) {
          console.log(`Restore message already shown for app_id: ${appId}, skipping...`);
          return;
        }

        console.log('Restoring flow from flow_json:', appInfo.flow_json);

        // 尝试解析flow_json
        const flowData = JSON.parse(appInfo.flow_json);

        if (flowData.nodes && flowData.edges) {
          // 恢复节点和边
          setNodes(flowData.nodes);
          setEdges(flowData.edges);

          // 显示恢复成功的消息，并标记为已显示
          // showInfo('ワークフローが正常に復元されました');
          hasShownRestoreMessage.current[appId] = true;
        }
      } catch (error) {
        console.error('Failed to parse flow_json:', error);
      }
    }
  }, [appInfo, setNodes, setEdges]);

  const onNodesChange = useCallback(
    (changes: any) => setNodes((nds) => applyNodeChanges(changes, nds)),
    [],
  );

  const onEdgesChange = useCallback(
    (changes: any) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    [],
  );

  const onConnect = useCallback(
    (params: any) => setEdges((eds) => addEdge(params, eds)),
    [setEdges],
  );

  // 轮询工作流状态
  const pollWorkflowStatus = useCallback(async (workflowRunId: string) => {
    if (!appInfo?.app_id) {
      console.log('No app_id available for polling');
      return;
    }

    try {
      console.log(`轮询工作流状态 app_id: ${appInfo.app_id}, workflow_run_id: ${workflowRunId}`);
      const status = await getWorkflowStatus(appInfo.app_id, workflowRunId);
      console.log('工作流状态响应:', status);
      console.log('API返回的节点详情:', status.node_details);

      // 更新节点状态
      const newNodeStatuses: {[nodeId: string]: NodeStatus} = {};

      // 获取所有前端节点ID
      const allNodeIds = nodes.map(n => n.id);
      console.log('所有前端节点ID:', allNodeIds);

      // 如果工作流还在运行中，我们需要推断哪些节点正在运行
      if (status.status === 'running') {
        console.log('工作流正在运行中，推断节点状态...');

        // 首先设置所有已完成的节点
        const completedNodeIds = new Set();
        status.node_details.forEach(nodeDetail => {
          newNodeStatuses[nodeDetail.node_id] = nodeDetail;
          completedNodeIds.add(nodeDetail.node_id);
          console.log(`节点 ${nodeDetail.node_id} 已完成，状态: ${nodeDetail.status}`);
        });

        // 推断正在运行的节点：还没有在node_details中的节点
        const pendingNodeIds = allNodeIds.filter(id => !completedNodeIds.has(id));
        console.log('待执行的节点:', pendingNodeIds);

        // 简化逻辑：如果有待执行的节点，将第一个设为RUNNING状态
        if (pendingNodeIds.length > 0) {
          const runningNodeId = pendingNodeIds[0];
          newNodeStatuses[runningNodeId] = {
            node_id: runningNodeId,
            status: 'RUNNING',
            started_at: new Date().toISOString(),
            finished_at: undefined,
            output_type: '',
            output: ''
          };
          console.log(`推断节点 ${runningNodeId} 正在运行`);
        }

      } else {
        // 工作流已完成，直接使用API返回的状态
        status.node_details.forEach(nodeDetail => {
          newNodeStatuses[nodeDetail.node_id] = nodeDetail;
          console.log(`节点 ${nodeDetail.node_id} 最终状态: ${nodeDetail.status}`);
        });
      }

      setNodeStatuses(newNodeStatuses);

      console.log('Current nodes before update:', nodes.map(n => ({ id: n.id, type: n.type })));

      // 更新节点视觉状态
      setNodes(currentNodes => {
        console.log('=== 开始更新节点状态 ===');
        console.log('当前节点:', currentNodes.map(n => ({ id: n.id, currentBg: n.style?.backgroundColor })));
        console.log('新状态数据:', newNodeStatuses);

        const updatedNodes = currentNodes.map(node => {
          const nodeStatus = newNodeStatuses[node.id];
          console.log(`检查节点 ${node.id}, 找到状态:`, nodeStatus);

          if (nodeStatus) {
            const backgroundColor = getNodeBackgroundColor(nodeStatus.status);
            // const borderColor = getNodeBorderColor(nodeStatus.status);
            const className = `node-${nodeStatus.status.toLowerCase()}`;

            console.log(`更新节点 ${node.id}:`);
            console.log(`  - 状态: ${nodeStatus.status}`);
            console.log(`  - 背景色: ${backgroundColor}`);
            // console.log(`  - 边框色: ${borderColor}`);
            console.log(`  - 类名: ${className}`);
            console.log(`  - 原始样式:`, node.style);

            const newStyle = {
              ...node.style,
              backgroundColor: `${backgroundColor} !important`,
              // border: `2px solid ${borderColor} !important`
            };
            console.log(`  - 新样式:`, newStyle);

            return {
              ...node,
              data: {
                ...node.data,
                status: nodeStatus.status
              },
              style: newStyle,
              className: `${node.className || ''} ${className}`.trim()
            };
          }
          console.log(`节点 ${node.id} 没有状态更新`);
          return node;
        });

        console.log('Updated nodes:', updatedNodes.map(n => ({
          id: n.id,
          status: n.data?.status,
          backgroundColor: n.style?.backgroundColor,
          className: n.className
        })));

        return updatedNodes;
      });

      // 如果工作流完成，停止轮询
      if (status.status === 'completed' || status.status === 'failed') {
        console.log(`Workflow ${status.status}, stopping polling`);
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
        setCurrentWorkflowRunId(null);

        if (status.status === 'completed') {
          showInfo('ワークフローが正常に完了しました');
        } else {
          showInfo('ワークフローが失敗しました');
        }
      }
    } catch (error) {
      console.error('Failed to poll workflow status:', error);
    }
  }, [appInfo?.app_id, setNodes, nodes]);

  // 获取节点背景颜色
  const getNodeBackgroundColor = (status: string): string => {
    switch (status) {
      case 'RUNNING':
        return '#e8f5e8'; // 浅绿色
      case 'SUCCESS':
        return '#e3f2fd'; // 浅蓝色
      case 'FAILED':
        return '#ffebee'; // 浅红色
      case 'PENDING':
      default:
        return '#ffffff'; // 白色
    }
  };

  // 获取节点边框颜色
  // const getNodeBorderColor = (status: string): string => {
  //   switch (status) {
  //     case 'RUNNING':
  //       return '#4caf50'; // 绿色
  //     case 'SUCCESS':
  //       return '#2196f3'; // 蓝色
  //     case 'FAILED':
  //       return '#f44336'; // 红色
  //     case 'PENDING':
  //     default:
  //       return '#e0e0e0'; // 灰色
  //   }
  // };

  // 重置所有节点状态为初始状态
  const resetAllNodesStatus = useCallback(() => {
    console.log('重置所有节点状态为初始状态');
    setNodes(currentNodes => {
      const updatedNodes = currentNodes.map(node => {
        const backgroundColor = getNodeBackgroundColor('PENDING');
        // const borderColor = getNodeBorderColor('PENDING');
        console.log(`重置节点 ${node.id} 为PENDING状态, backgroundColor: ${backgroundColor}`);

        return {
          ...node,
          data: {
            ...node.data,
            status: 'PENDING'
          },
          style: {
            ...node.style,
            backgroundColor: `${backgroundColor} !important`,
            // border: `2px solid ${borderColor} !important`
          },
          className: `${node.className || ''} node-pending`.trim()
        };
      });

      console.log('所有节点已重置为PENDING:', updatedNodes.map(n => ({
        id: n.id,
        status: n.data?.status,
        backgroundColor: n.style?.backgroundColor,
        borderColor: n.style?.border
      })));

      return updatedNodes;
    });
  }, [setNodes]);

  // 开始轮询工作流状态
  const startPolling = useCallback((workflowRunId: string) => {
    console.log('开始工作流状态轮询:', workflowRunId);
    setCurrentWorkflowRunId(workflowRunId);

    // 清除之前的轮询
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    // 重置所有节点状态为初始状态
    resetAllNodesStatus();

    // 立即执行一次状态查询
    pollWorkflowStatus(workflowRunId);

    // 每2秒轮询一次
    pollingIntervalRef.current = setInterval(() => {
      pollWorkflowStatus(workflowRunId);
    }, 2000);
  }, [pollWorkflowStatus, resetAllNodesStatus]);

  // 清理轮询
  useEffect(() => {
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, []);

  return (
    <div className="workflow-container">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        className="react-flow"
        onInit={setRfInstance}
        style={{ backgroundColor: bgColor }}
      >
        <Background />
        <Panel position="bottom-right" style={{ bottom: '30px' }}>
          <MiniMap />
        </Panel>

        <Panel position="bottom-left" style={{ bottom: '32px' }}>
          <Toolbar />
        </Panel>
        <Panel position="bottom-center" style={{ bottom: '45px' }}>
          <AddNode />
        </Panel>
        <Panel position="top-right">
          <Header
            rfInstance={rfInstance}
            flowKey={flowKey}
            setNodes={setNodes}
            setEdges={setEdges}
            getNodeId={getNodeId}
            onWorkflowRun={startPolling}
          />
        </Panel>
      </ReactFlow>

      {/* 显示节点详情面板 */}
      {selectedNode && (
        <div>
          {selectedNode.type === 'startNode' && (
            <StartNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
          {selectedNode.type === 'endNode' && (
            <EndNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
          {selectedNode.type === 'llmNode' && (
            <LLMNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
          {selectedNode.type === 'datasetGrepNode' && (
            <DatasetGrepNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
          {selectedNode.type === 'codeNode' && (
            <CodeNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
          {selectedNode.type === 'httpRequestNode' && (
            <HttpRequestNodeDetailPanel
              node={selectedNode}
              onClose={clearSelectedNode}
            />
          )}
        </div>
      )}
    </div>
  );
};

// 主组件，提供上下文
const Workflow: React.FC<WorkflowProps> = ({ appInfo }) => {
  return (
    <NodeEventProvider>
      <WorkflowContent appInfo={appInfo} />
    </NodeEventProvider>
  );
};

export default Workflow;
