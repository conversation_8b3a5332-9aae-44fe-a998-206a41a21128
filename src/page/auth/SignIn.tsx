import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaGithub, FaGoogle } from 'react-icons/fa';
import { login } from '../../services/authService';
import { showError, showSuccess } from '../../utils/toast';
import './css/SignIn.css';

const SignIn: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 验证表单
    if (!email.trim()) {
      showError('メールアドレスを入力してください');
      return;
    }

    if (!password.trim()) {
      showError('パスワードを入力してください');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // 调用登录API
      await login(email, password);

      // 显示成功消息
      showSuccess('ログインに成功しました');

      // 登录成功，导航到应用页面
      navigate('/apps');
    } catch (error) {
      console.error('Login failed:', error);
      showError('ログインに失敗しました。メールアドレスとパスワードを確認してください。');
      setError('ログインに失敗しました。メールアドレスとパスワードを確認してください。');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="signin-container">
      <div className="signin-card">
        <div className="signin-content">
          <h1 className="signin-title">AfireAI</h1>

          <h2 className="signin-subtitle">はじめましょう！👋</h2>
          <p className="signin-description">AfireAIへようこそ。続行するにはログインしてください。</p>

          <div className="social-buttons">
            {/* <button className="social-button github">
              <FaGithub className="social-icon" />
              <span>GitHubで続行</span>
            </button> */}

            <button className="social-button google">
              <FaGoogle className="social-icon" />
              <span>Googleで続行</span>
            </button>
          </div>

          <div className="divider">
            <span>または</span>
          </div>

          <form onSubmit={handleSubmit} className="signin-form">
            {error && <div className="error-message">{error}</div>}

            <div className="form-group">
              <label htmlFor="email">メールアドレス</label>
              <input
                type="email"
                id="email"
                placeholder="メールアドレスを入力してください"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isLoading}
              />
            </div>

            <div className="form-group">
              <label htmlFor="password">パスワード</label>
              <input
                type="password"
                id="password"
                placeholder="パスワードを入力してください"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={isLoading}
              />
            </div>

            <button
              type="submit"
              className="signin-button"
              disabled={isLoading}
            >
              {isLoading ? 'ログイン中...' : 'ログイン'}
            </button>
          </form>

        </div>
      </div>
    </div>
  );
};

export default SignIn;
