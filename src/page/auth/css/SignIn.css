.signin-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f5f7f9;
    padding: 20px;
    width: 100%;
    box-sizing: border-box;
}

.signin-card {
    width: 100%;
    max-width: 480px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.signin-content {
    padding: 32px 24px;
}

.signin-title {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 24px 0;
    text-align: center;
    color: #4a90e2;
}

.signin-subtitle {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 8px 0;
    text-align: center;
}

.signin-description {
    font-size: 14px;
    color: #666;
    margin: 0 0 24px 0;
    text-align: center;
}

.social-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 24px;
}

.social-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: white;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.social-button:hover {
    background-color: #f5f5f5;
}

.social-icon {
    font-size: 14px;
}

.github {
    color: #333;
}

.google {
    color: #4285F4;
}

.divider {
    display: flex;
    align-items: center;
    margin: 24px 0;
    color: #999;
    font-size: 12px;
}

.divider::before,
.divider::after {
    content: "";
    flex: 1;
    border-bottom: 1px solid #e0e0e0;
}

.divider span {
    padding: 0 10px;
}

.signin-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-group label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.form-group input {
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    background-color: #f5f7f9;
}

.form-group input:focus {
    outline: none;
    border-color: #4a90e2;
}

.signin-button {
    margin-top: 8px;
    padding: 12px;
    background-color: #4a90e2;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.signin-button:hover {
    background-color: #3a7bc8;
}

.signin-button:disabled {
    background-color: #a0c4f0;
    cursor: not-allowed;
}

.error-message {
    padding: 10px;
    background-color: #ffebee;
    color: #d32f2f;
    border-radius: 4px;
    font-size: 12px;
    margin-bottom: 8px;
}