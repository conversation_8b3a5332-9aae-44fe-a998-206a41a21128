// 认证服务 - 处理登录、令牌管理和存储

// 登录请求接口
export interface LoginRequest {
  email: string;
  password: string;
}

// 登录响应接口
export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

// 认证状态接口
export interface AuthState {
  isLoggedIn: boolean;
  accessToken: string | null;
  refreshToken: string | null;
}

// 本地存储键
const ACCESS_TOKEN_KEY = 'access_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const IS_LOGGED_IN_KEY = 'is_logged_in';

// API基础URL
const API_BASE_URL = '/api';

// 获取认证状态
export const getAuthState = (): AuthState => {
  const isLoggedIn = localStorage.getItem(IS_LOGGED_IN_KEY) === 'true';
  const accessToken = localStorage.getItem(ACCESS_TOKEN_KEY);
  const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
  
  return {
    isLoggedIn,
    accessToken,
    refreshToken
  };
};

// 获取访问令牌
export const getAccessToken = (): string | null => {
  return localStorage.getItem(ACCESS_TOKEN_KEY);
};

// 获取刷新令牌
export const getRefreshToken = (): string | null => {
  return localStorage.getItem(REFRESH_TOKEN_KEY);
};

// 设置认证状态
export const setAuthState = (state: AuthState): void => {
  if (state.isLoggedIn) {
    localStorage.setItem(IS_LOGGED_IN_KEY, 'true');
    if (state.accessToken) {
      localStorage.setItem(ACCESS_TOKEN_KEY, state.accessToken);
    }
    if (state.refreshToken) {
      localStorage.setItem(REFRESH_TOKEN_KEY, state.refreshToken);
    }
  } else {
    localStorage.removeItem(IS_LOGGED_IN_KEY);
    localStorage.removeItem(ACCESS_TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
  }
};

// 登录
export const login = async (email: string, password: string): Promise<LoginResponse> => {
  try {
    console.log('Logging in with:', { email, password });
    
    const response = await fetch(`${API_BASE_URL}/users/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        email,
        password
      } as LoginRequest)
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'No error details');
      console.error(`Login error: ${response.status}`, errorText);
      throw new Error(`Login failed: ${response.status} - ${errorText}`);
    }

    const data = await response.json() as LoginResponse;
    console.log('Login successful:', data);
    
    // 保存认证状态
    setAuthState({
      isLoggedIn: true,
      accessToken: data.access_token,
      refreshToken: data.refresh_token
    });
    
    return data;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

// 登出
export const logout = (): void => {
  console.log('Logging out');
  
  // 清除认证状态
  setAuthState({
    isLoggedIn: false,
    accessToken: null,
    refreshToken: null
  });
};

// 刷新令牌
export const refreshToken = async (): Promise<LoginResponse> => {
  try {
    const refreshToken = getRefreshToken();
    
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }
    
    const response = await fetch(`${API_BASE_URL}/users/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        refresh_token: refreshToken
      })
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'No error details');
      console.error(`Token refresh error: ${response.status}`, errorText);
      throw new Error(`Token refresh failed: ${response.status} - ${errorText}`);
    }

    const data = await response.json() as LoginResponse;
    console.log('Token refresh successful:', data);
    
    // 更新认证状态
    setAuthState({
      isLoggedIn: true,
      accessToken: data.access_token,
      refreshToken: data.refresh_token
    });
    
    return data;
  } catch (error) {
    console.error('Token refresh error:', error);
    
    // 如果刷新失败，清除认证状态
    logout();
    
    throw error;
  }
};

// 检查是否已登录
export const isAuthenticated = (): boolean => {
  return getAuthState().isLoggedIn;
};
