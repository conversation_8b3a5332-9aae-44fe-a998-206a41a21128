// 应用服务API

// 创建应用的接口
export interface CreateAppRequest {
  app_name: string;
  app_description: string;
  app_icon_file: string;
  tag: string;
  flow_json: string;
}

// 应用响应接口
export interface AppResponse {
  app_name: string;
  app_description: string;
  app_icon_file: string;
  tag: string;
  flow_json: string;
  user_id: string;
  ip_addr: string;
  app_id: string;
  created_at: string;
  updated_at: string;
}

// API基础URL - 使用Vite代理
const API_BASE_URL = '/api';

// 从认证服务获取令牌
import { getAccessToken } from './authService';

// 获取认证令牌
const getAuthToken = (): string | null => {
  return getAccessToken();
};

// 创建应用
export const createApp = async (appName: string, appDescription: string, appIconFile: string, tag: string = 'workflow', flowJson: string = ''): Promise<AppResponse> => {
  try {
    console.log('Creating app with data:', { appName, appDescription, appIconFile, tag, flowJson });

    const requestData = {
      app_name: appName,
      app_description: appDescription,
      app_icon_file: appIconFile,
      tag: tag,
      flow_json: flowJson
    };

    console.log('Request payload:', JSON.stringify(requestData));

    const response = await fetch(`${API_BASE_URL}/apps/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {}),
        'Accept': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      },
      credentials: 'include',
      mode: 'cors',
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'No error details');
      console.error(`API error: ${response.status}`, errorText);
      throw new Error(`API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('API response:', data);
    return data as AppResponse;
  } catch (error) {
    console.error('Error creating app:', error);
    throw error;
  }
};

// 获取应用列表
export const getApps = async (): Promise<AppResponse[]> => {
  try {
    const response = await fetch(`${API_BASE_URL}/apps/list`, {
      method: 'GET',
      headers: {
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json() as AppResponse[];
  } catch (error) {
    console.error('Error fetching apps:', error);
    throw error;
  }
};

// 获取应用详情
export const getAppById = async (appId: string): Promise<AppResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/apps/${appId}`, {
      method: 'GET',
      headers: {
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json() as AppResponse;
  } catch (error) {
    console.error(`Error fetching app ${appId}:`, error);
    throw error;
  }
};

// 更新应用
export const updateApp = async (appId: string, appData: Partial<CreateAppRequest>): Promise<AppResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/apps/${appId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
      },
      body: JSON.stringify(appData)
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json() as AppResponse;
  } catch (error) {
    console.error(`Error updating app ${appId}:`, error);
    throw error;
  }
};

// 删除应用
export const deleteApp = async (appId: string): Promise<void> => {
  try {
    const response = await fetch(`${API_BASE_URL}/apps/${appId}`, {
      method: 'DELETE',
      headers: {
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
  } catch (error) {
    console.error(`Error deleting app ${appId}:`, error);
    throw error;
  }
};

// 运行工作流
export const runWorkflow = async (appId: string): Promise<void> => {
  try {
    const response = await fetch(`${API_BASE_URL}/run/${appId}/workflow`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(getAuthToken() ? { 'Authorization': `Bearer ${getAuthToken()}` } : {})
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
  } catch (error) {
    console.error(`Error running workflow for app ${appId}:`, error);
    throw error;
  }
};
