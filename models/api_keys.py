from sqlalchemy import Column, String, DateTime
from database.db import Base
from datetime import datetime

class APIKey(Base):
    __tablename__ = "api_keys"

    user_id = Column(String(36), primary_key=True, nullable=False)

    api_name = Column(String(255), nullable=True)
    api_key = Column(String(255), nullable=False, unique=True)

    expiration = Column(DateTime, nullable=True)
    memo = Column(String(255), nullable=True)
    ip_addr = Column(String(20), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, nullable=False, onupdate=datetime.utcnow)