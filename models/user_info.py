from sqlalchemy import Column, String, DateTime
from database.db import Base  # 既存のBaseを使用

class UserInfo(Base):
    __tablename__ = "user_info"

    user_id = Column(String(36), primary_key=True, nullable=False)
    company_id = Column(String(20), nullable=False)
    nick_name = Column(String(20))
    last_nm_kanji = Column(String(20))
    first_nm_kanji = Column(String(20))
    last_nm_kana = Column(String(20))
    first_nm_kana = Column(String(20))
    email = Column(String(200))
    phone = Column(String(15))
    sex = Column(String(1))
    birth = Column(String(8))
    address = Column(String(200))
    is_delete = Column(String(1), default="0")
    ip_addr = Column(String(20))
    created_at = Column(DateTime)
    updated_at = Column(DateTime)