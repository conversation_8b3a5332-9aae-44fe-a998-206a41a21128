# models/user.py

from sqlalchemy import Column, String, DateTime
from database.db import Base
from datetime import datetime

class User(Base):
    __tablename__ = "user"

    id = Column(String(36), primary_key=True, nullable=False)
    store_id = Column(String(36), nullable=False, default='100')
    email = Column(String(150), nullable=False)
    username = Column(String(100), nullable=False)
    password = Column(String(300), nullable=False)
    nick_name = Column(String(20), default='', comment='ニックネーム')
    avatar = Column(String(300), nullable=False, default='')
    role = Column(String(20), nullable=False, default='SITE_USER')
    user_roles = Column(String(100), nullable=False)
    status = Column(String(1), nullable=False, default='0')
    platform = Column(String(20), nullable=False, default='')
    online = Column(String(1), nullable=False, default='')
    last_connected_dtm = Column(DateTime, default=None, comment='直近ログイン日時')
    ip_addr = Column(String(20), default=None)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, nullable=False, onupdate=datetime.utcnow)