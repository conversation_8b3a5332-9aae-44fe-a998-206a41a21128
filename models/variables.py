# models/variables.py

from sqlalchemy import Column, String, DateTime
from database.db import Base
from datetime import datetime, timezone

class Variable(Base):
    __tablename__ = "variables"

    id = Column(String(36), primary_key=True, nullable=False)  # ID
    user_id = Column(String(36), nullable=False)  # ユーザーID
    workflow_run_id = Column(String(36), nullable=False)  # work flow run id
    var_key = Column(String(255), nullable=False)  # 変数ID
    var_val = Column(String(255), nullable=False)  # 変数値
    ip_addr = Column(String(20), nullable=False)  # IPアドレス
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)  # 作成タイムスタンプ
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False, onupdate=lambda: datetime.now(timezone.utc))  # 更新タイムスタンプ
