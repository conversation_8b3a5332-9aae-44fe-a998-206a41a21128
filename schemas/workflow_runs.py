# schemas/workflow_runs.py

from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class WorkflowRunBase(BaseModel):
    user_id: Optional[str] = None
    app_id: str
    status: Optional[str] = None
    params: Optional[str] = None
    memory: Optional[str] = None
    flow_json: str
    ip_addr: Optional[str] = None

class WorkflowRunCreate(WorkflowRunBase):
    finished_at: Optional[datetime] = None  # 创建时可选，完成时更新

class WorkflowRunUpdate(BaseModel):
    status: Optional[str] = None
    params: Optional[str] = None
    memory: Optional[str] = None
    flow_json: Optional[str] = None
    finished_at: Optional[datetime] = None

class WorkflowRunResponse(WorkflowRunBase):
    workflow_run_id: str
    started_at: datetime
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True  # Pydantic v2対応
