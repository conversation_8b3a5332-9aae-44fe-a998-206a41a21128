# schemas/workflow_run_nodes.py

from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class WorkflowRunNodeBase(BaseModel):
    user_id: Optional[str] = None
    workflow_run_id: str
    node_id: str
    node_ver: str
    status: Optional[str] = None
    params: Optional[str] = None
    memory: Optional[str] = None
    flow_json: str
    output_type: str
    output: str
    ip_addr: Optional[str] = None

class WorkflowRunNodeCreate(WorkflowRunNodeBase):
    finished_at: Optional[datetime] = None  # 创建时可选，完成时更新

class WorkflowRunNodeUpdate(BaseModel):
    status: Optional[str] = None
    params: Optional[str] = None
    memory: Optional[str] = None
    flow_json: Optional[str] = None
    output_type: Optional[str] = None
    output: Optional[str] = None
    finished_at: Optional[datetime] = None

class WorkflowRunNodeResponse(WorkflowRunNodeBase):
    id: str
    started_at: datetime
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True  # Pydantic v2対応
