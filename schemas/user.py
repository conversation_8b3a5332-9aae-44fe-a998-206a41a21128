# schemas/user.py

from pydantic import BaseModel
from datetime import datetime

class UserBase(BaseModel):
    email: str
    username: str
    password: str
    nick_name: str = ""
    avatar: str = ""
    role: str = "SITE_USER"
    user_roles: str
    status: str = "0"
    platform: str = ""
    online: str = ""
    last_connected_dtm: datetime | None = None
    ip_addr: str | None = None

class UserCreate(UserBase):
    pass

class UserResponse(UserBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        # orm_mode = True
        from_attributes = True  # Pydantic v2対応