# schemas/variables.py

from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class VariableBase(BaseModel):
    user_id: Optional[str] = None
    workflow_run_id: str
    var_key: str
    var_val: str
    ip_addr: Optional[str] = None

class VariableCreate(VariableBase):
    pass

class VariableUpdate(BaseModel):
    var_val: str

class VariableResponse(VariableBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True  # Pydantic v2対応
