# schemas/apps.py

from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class AppBase(BaseModel):
    app_name: Optional[str] = None
    app_description: Optional[str] = None
    app_icon_file: Optional[str] = None
    tag: Optional[str] = None
    flow_json: str
    user_id: Optional[str] = None
    ip_addr: Optional[str] = None

class AppCreate(AppBase):
    pass

class AppUpdate(BaseModel):
    app_name: Optional[str] = None
    app_description: Optional[str] = None
    app_icon_file: Optional[str] = None
    tag: Optional[str] = None
    flow_json: Optional[str] = None

class AppResponse(AppBase):
    app_id: str
    user_id: str
    ip_addr: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True  # Pydantic v2対応
