from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class APIKeyBase(BaseModel):
    api_name: str
    expiration: Optional[datetime]
    memo: str = None
    ip_addr: str = None

class APIKeyCreate(APIKeyBase):
    pass

class APIKeyResponse(APIKeyBase):
    api_key: str
    user_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True  # Pydantic v2対応